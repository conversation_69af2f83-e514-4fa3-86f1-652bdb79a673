from fastapi import Depends
from fastapi.security import OAuth2PasswordBearer
from datetime import datetime, timedelta
from passlib.context import CryptContext
from db.connection import db_manager
from dotenv import load_dotenv
from core.logs import logger
import os
import jwt

load_dotenv()

SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")
TOKEN_TIME_EXPIRE = os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES")

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_user(username: str):
    query = "SELECT * FROM gatchan_app.user WHERE username = %s"
    params = (username,)
    result = db_manager.execute_query(query, params)
    return result[0] if result else None

def get_user_crm(username: str):
    query = "SELECT * FROM gatchan.users_admin WHERE username = %s"
    params = (username,)
    result = db_manager.execute_query(query, params)
    return result[0] if result else None

def get_user_by_email(email: str):
    query = "SELECT uuid,email FROM gatchan.users WHERE email = %s"
    params = (email,)
    result = db_manager.execute_query(query, params)
    return result[0] if result else None

def get_user_by_uuid(UUID: str):
    query = "SELECT uuid,username,id FROM gatchan_app.user WHERE uuid = %s"
    params = (UUID,)
    result = db_manager.execute_query(query, params)
    return result[0] if result else None

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=int(TOKEN_TIME_EXPIRE))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def decode_token(token: str):
    return jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

def check_token(token: str = Depends(oauth2_scheme)):
    decode = decode_token(token)

    # Calcular el tiempo restante hasta que el token expire
    time_to_expire = timedelta(seconds=decode["exp"] - datetime.utcnow().timestamp())

    # Comprobar si el token ha expirado
    if decode["exp"] < datetime.utcnow().timestamp():
        return {"status": False, "message": "El token ha expirado"}

    # Convertir el tiempo restante a un formato de días, horas y minutos
    days = time_to_expire.days
    hours, remainder = divmod(time_to_expire.seconds, 3600)
    minutes, _ = divmod(remainder, 60)

    # Si queda más de un día, muestra los días, si no, muestra las horas y minutos
    if days > 0:
        time_string = f"{days} día{'s' if days > 1 else ''}"
    else:
        time_string = f"{hours} hora{'s' if hours != 1 else ''} y {minutes} minuto{'s' if minutes != 1 else ''}"

    return {"status": True, "message": "El token es válido", "exp": time_string}


def get_current_user(token: str = Depends(oauth2_scheme)):
    user = decode_token(token)
    return user["username"]

def save_token(token: str, UUID: str, forgot_password: bool = False):
    try:
        if not forgot_password:
            # Primero, intentamos obtener el token existente para el UUID dado
            query_get = "SELECT token FROM token WHERE uuid = %s"
            existing_token = db_manager.execute_query(query_get, (UUID,))

            # Si no encontramos un token para el UUID
            if not existing_token:
                query_insert = "INSERT INTO token (uuid, token) VALUES (%s, %s)"
                db_manager.execute_query(query_insert, (UUID, token))
                return {"status": "success", "message": "Token added"}

            # Si encontramos un token pero es diferente del nuevo token
            elif existing_token[0][0] != token:
                query_update = "UPDATE token SET token = %s WHERE uuid = %s"
                db_manager.execute_query(query_update, (token, UUID))
                return {"status": "success", "message": "Token updated"}

            # Si el token existente es el mismo que el nuevo token
            else:
                return {"status": "unchanged", "message": "Token is the same, no action taken"}
        else:
            # Primero, intentamos obtener el token existente para el UUID dado
            query_get = "SELECT token, expire FROM forgot_password WHERE uuid = %s"
            existing_token = db_manager.execute_query(query_get, (UUID,))

            # Comprobamos si el token lleva más de 24 horas activo y si es así lo eliminamos de la base de datos
            if existing_token and existing_token[0][1] < datetime.utcnow():
                query_delete = "DELETE FROM forgot_password WHERE uuid = %s"
                db_manager.execute_query(query_delete, (UUID,))
                return {"status": "success", "message": "Token deleted", "code": 200, "action": "delete", "token": None}

            # Si no encontramos un token para el UUID, o si el anterior fue borrado por expiración
            if not existing_token:
                expiration_time = datetime.utcnow() + timedelta(hours=24)
                query_insert = "INSERT INTO forgot_password (uuid, token, expire) VALUES (%s, %s, %s)"
                db_manager.execute_query(query_insert, (UUID, token, expiration_time))
                query_get = "SELECT token, expire FROM forgot_password WHERE uuid = %s"
                existing_token = db_manager.execute_query(query_get, (UUID,))
                return {"status": "success", "message": "Token added", "expire": existing_token[0][1].timestamp(), "code": 200, "action": "add", "token": existing_token[0][0]}

            # Si el token no ha expirado, lo devolvemos para que el usuario pueda usarlo
            else:
                return {"status": "success", "message": "Token is the same, no action taken", "expire": existing_token[0][1].timestamp(), "code": 200, "action": "return", "token": existing_token[0][0]}

    except Exception as e:
        logger("error", f"An error occurred while handling the token: {e}")
        return {"status": "error", "message": "An error occurred while handling the token.", "code": 500, "action": "error"}
        
def delete_token(token: str):
    try:
        query_delete = "DELETE FROM forgot_password WHERE token = %s"
        db_manager.execute_query(query_delete, (token,))
        return True
    except Exception as e:
        logger("error", f"An error occurred while deleting the token: {e}")
        return False