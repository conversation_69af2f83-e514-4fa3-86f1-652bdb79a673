import json
import re
from main import app
from starlette.responses import StreamingResponse
import boto3
from decouple import config
from botocore.exceptions import NoCredentialsError
from fastapi import HTTPException, Depends
from fastapi.responses import StreamingResponse
from core.utils import requires_auth

def iter_bytes(data):
    yield data

async def get_closest_match(id: str, date: str):
    try:
        ACCESS_KEY = config("ACCESS_KEY_INVOICE")
        SECRET_KEY = config("SECRET_KEY_INVOICE")

        bucket_name = "invoice-generator-data-pdf"

        s3 = boto3.resource(
            "s3",
            region_name="eu-central-1",
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
        )

        # Get all objects in the bucket
        bucket = s3.Bucket(bucket_name)
        pdf_files = [obj.key for obj in bucket.objects.all() if obj.key.endswith('.pdf')]

        # Filter the files that match the given id and date
        matching_files = [file for file in pdf_files if re.match(f'{id}/{date}GT', file)]

        # If there are matching files, return the first one
        if matching_files:
            return matching_files[0]

    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(status_code=500, detail="Error retrieving invoice")

@app.get("/invoice")
async def invoice(id: str = "0", date: str ="0"):
    try:
        item_name = await get_closest_match(id, date)

        ACCESS_KEY = config("ACCESS_KEY_INVOICE")
        SECRET_KEY = config("SECRET_KEY_INVOICE")

        bucket_name = "invoice-generator-data-pdf"

        s3 = boto3.resource(
            "s3",
            region_name="eu-central-1",
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
        )
        obj = s3.Object(bucket_name, item_name)
        fs = obj.get()["Body"].read()

        response = StreamingResponse(iter_bytes(fs), media_type="application/pdf")
        response.headers["Content-Disposition"] = 'inline; filename="invoice.pdf"'

    except Exception as e:
        print("Exception: ", e)
        response = StreamingResponse(None, media_type="application/pdf")
    return response


@app.get("/get_s3_objects/{user_id}")
async def get_s3_objects(user_id: str = "0"):
# async def get_s3_objects(user=Depends(requires_auth), user_id: str = "0"):
    ACCESS_KEY = config("ACCESS_KEY_INVOICE")
    SECRET_KEY = config("SECRET_KEY_INVOICE")
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY,
                      aws_secret_access_key=SECRET_KEY)

    bucket_name = 'invoice-generator-data-pdf'
    prefix = user_id + '/'

    try:
        objects = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
        if objects['KeyCount'] > 0:
            table = []
            for i in range(objects['KeyCount']):
                key = objects['Contents'][i]['Key']
                url = s3.generate_presigned_url('get_object', Params={'Bucket': bucket_name, 'Key': key}, ExpiresIn=60*60*24)
                table.append({"id": key.split('/')[1], "url": url})

            for i in range(len(table)):
                id = table[i]['url'].split('/')[-2]
                table[i]['url'] = table[i]['url'].split('.pdf')[0] + '.pdf'
                
                YYYYMM = table[i]['url'].split('/')[-1].split('GT')[0]
                month_names = {1: 'January', 2: 'February', 3: 'March', 4: 'April', 5: 'May', 6: 'June', 7: 'July', 8: 'August', 9: 'September', 10: 'October', 11: 'November', 12: 'December'}
                
                # Add name and date
                table[i]['id'] = id
                table[i]['name'] = 'Invoice #' + YYYYMM
                table[i]['date'] = month_names[int(YYYYMM[-2:])] + ' ' + YYYYMM[:4]
                
                # del table[i]['url']
            
            return table
        else:
            return {"message": "No objects found for the given user_id."}
    except NoCredentialsError:
        return {"error": "Credentials not available"}


async def extract_gt_to_pdf(user_id: str, date: str):
    # Call the existing function
    objects = await get_s3_objects(user_id)

    # Process the output
    for obj in objects:
        match = re.search(f'{date}(GT.+\.pdf)', obj['url'])
        if match:
            return match.group(1)

    # If no match is found, return None
    return None