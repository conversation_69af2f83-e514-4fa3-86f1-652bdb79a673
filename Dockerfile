# Use the official Python image
FROM python:3.8-slim

# Set the working directory
WORKDIR /usr/src/app

# Copy requirements file into the container
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt uvicorn fastapi cryptography
# RUN pip install --no-cache-dir -r requirements.txt

# INVOICE PDF
RUN pip install pdfkit jinja2

RUN pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client

# Install wkhtmltopdf
RUN apt-get update && apt-get install -y \
    wkhtmltopdf \
    && rm -rf /var/lib/apt/lists/*

# Set the XDG_RUNTIME_DIR environment variable
ENV XDG_RUNTIME_DIR /tmp/runtime-root

# Copy the rest of the project into the container
COPY . .