from passlib.context import CryptContext
from db.connection import db_manager
from endpoints.models.auth.user import User
from core.logs import logger
from dotenv import load_dotenv
import random
import os

load_dotenv()

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str):
    return pwd_context.hash(password)

def check_user_exists(username: str):
    user_find = False
    query = "SELECT uuid FROM gatchan.users WHERE email = %s"
    params = (username,)
    result = db_manager.execute_query(query, params)

    if result:
        user_find = True

    return user_find

def register(user: User):
    try:
        # Generar UUID
        query_uuid = "SELECT UUID() as uuid_value"
        uuid_result = db_manager.execute_query(query_uuid)
        uuid_value = uuid_result[0][0] if uuid_result else None

        if not uuid_value:
            logger("error", "Failed to generate UUID")
            return False

        username = user.email.split("@")[0]
        username = "".join([c for c in username if c.isalnum()])
        password = username + str(random.randint(1000, 9999))

        # Insertar en la tabla 'user'
        query_user = "INSERT INTO `user` (`uuid`, `username`, `password`) VALUES (%s, %s, %s)"
        params_user = (uuid_value, username, hash_password(password))
        db_manager.execute_query(query_user, params_user)

        # Insertar en la tabla 'billing'
        query_billing = "INSERT INTO `gatchan`.`billing` (`bank_account`, `address`, `postal_code`, `city`, `province`) VALUES (%s, %s, %s, %s, %s)"
        params_billing = (user.bank_account, user.billing_address, user.billing_postal_code, user.billing_city, user.billing_province)
        billing_id = db_manager.execute_query(query_billing, params_billing, True)

        # Insertar en la tabla 'users'
        query_user_data = "INSERT INTO `gatchan`.`users` (`uuid`, `name`, `national_id`, `mobile_phone`, `email`, `address`, `postal_code`, `city`, `province`, `billing_id`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
        params_user_data = (uuid_value, user.name, user.national_id, user.mobile_phone, user.email, user.address, user.postal_code, user.city, user.province, billing_id)
        user_insert_result = db_manager.execute_query(query_user_data, params_user_data)

        if user_insert_result is None:
            logger("error", "Failed to insert into users")
            return False

        return True
    except Exception as e:
        logger("error", "Error trying to register user: " + str(e))
        return False
    
def registerCRM(username: str, password: str):
    # Generar UUID
    query_uuid = "SELECT UUID() as uuid_value"
    uuid_result = db_manager.execute_query(query_uuid)
    uuid_value = uuid_result[0][0] if uuid_result else None

    if not uuid_value:
        logger("error", "Failed to generate UUID")
        return False
    
    # Insertar en la tabla 'user'
    query_user = "INSERT INTO gatchan.users_admin (`uuid`, `username`, `password`) VALUES (%s, %s, %s)"
    params_user = (uuid_value, username, hash_password(password))
    db_manager.execute_query(query_user, params_user)
    
    return True

def update_password(UUID: str, password: str):
    try:
        query = "UPDATE gatchan_app.user SET password = %s WHERE uuid = %s"
        params = (hash_password(password), UUID)
        db_manager.execute_query(query, params)
    except Exception as e:
        logger("error", "Error trying to update password: " + str(e))
        return False

    return True