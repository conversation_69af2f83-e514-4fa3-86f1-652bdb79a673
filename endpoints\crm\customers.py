import random
import uuid
from db.connection import db_manager
from core.logs import logger
from decouple import config
import json


def get_customers():
    query = (
        "SELECT uuid, name, email, mobile_phone, status, contact_id, national_id FROM gatchan.users"
    )
    result = db_manager.execute_query(query)

    if not result:
        logger("error", "Failed to retrieve customers")
        return False

    customers = []
    for row in result:
        customers.append(
            {
                "uuid": row[0],
                "first_name": row[1],
                "email": row[2],
                "mobile_phone": row[3],
                "status": row[4],
                "contact_id": row[5],
                "national_id": row[6],
            }
        )

    return customers


def get_customer(uuid: str):
    query = """
    SELECT 
        u.uuid, 
        u.name,
        u.contact_id, 
        u.national_id, 
        u.mobile_phone, 
        u.email, 
        u.address, 
        u.postal_code, 
        u.city, 
        u.province, 
        u.status, 
        u.isB2B, 
        u.provider_id, 
        b.bank_account, 
        b.address as billing_address, 
        b.postal_code as billing_postal_code, 
        b.city as billing_city, 
        b.province as billing_province,
        s.address as shipping_address,
        s.postal_code as shipping_postal_code,
        s.city as shipping_city,
        s.province as shipping_province
    FROM 
        gatchan.users u 
    JOIN 
        gatchan.billing b ON u.billing_id = b.id
    LEFT JOIN 
        gatchan.shipping s ON u.shipping_id = s.id
    WHERE 
        u.uuid = %s
    """
    params = (uuid,)

    try:
        result = db_manager.execute_query(query, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    if not result:
        logger("error", "Failed to retrieve customer")
        return None

    customer = {
        "uuid": result[0][0],
        "first_name": result[0][1],
        "contact_id": result[0][2],
        "national_id": result[0][3],
        "mobile_phone": result[0][4],
        "email": result[0][5],
        "address": result[0][6],
        "postal_code": result[0][7],
        "city": result[0][8],
        "province": result[0][9],
        "status": result[0][10],
        "isB2B": result[0][11],
        "provider_id": result[0][12],
        "bank_account": result[0][13],
        "billing_address": result[0][14],
        "billing_postal_code": result[0][15],
        "billing_city": result[0][16],
        "billing_province": result[0][17],
        "shipping_address": result[0][18],
        "shipping_postal_code": result[0][19],
        "shipping_city": result[0][20],
        "shipping_province": result[0][21],
    }

    return customer


def create_customer_(form_data):
    uuid4 = str(uuid.uuid4())
    contact_id = str(random.randint(*********, *********))
    name = form_data["first_name"]
    national_id = form_data["national_id"]
    mobile_phone = form_data["mobile_phone"]
    email = form_data["email"]
    address = form_data["address"]
    postal_code = form_data["postal_code"]
    city = form_data["city"]
    province = form_data["province"]
    bank_account = form_data["bank_account"]
    billing_address = form_data["billing_address"]
    billing_postal_code = form_data["billing_postal_code"]
    billing_city = form_data["billing_city"]
    billing_province = form_data["billing_province"]
    shipping_address = form_data["shipping_address"]
    shipping_postal_code = form_data["shipping_postal_code"]
    shipping_city = form_data["shipping_city"]
    shipping_province = form_data["shipping_province"]

    query = """
    INSERT INTO gatchan.users (
        uuid,
        name,
        contact_id,
        national_id,
        mobile_phone,
        email,
        address,
        postal_code,
        city,
        province,
        status,
        isB2B,
        provider_id,
        billing_id,
        shipping_id
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 1, 0, 1, %s, %s)
    """
    
    # Insert billing data
    query_billing = """
    INSERT INTO gatchan.billing (
        bank_account,
        address,
        postal_code,
        city,
        province
    ) VALUES (%s, %s, %s, %s, %s)
    """

    # Insert shipping data
    query_shipping = """
    INSERT INTO gatchan.shipping (
        address,
        postal_code,
        city,
        province
    ) VALUES (%s, %s, %s, %s)
    """

    params_billing = (
        bank_account,
        billing_address,
        billing_postal_code,
        billing_city,
        billing_province,
    )

    params_shipping = (
        shipping_address,
        shipping_postal_code,
        shipping_city,
        shipping_province,
    )


    try:
        b_id = db_manager.execute_query(query_billing, params_billing, True)
        q_id = db_manager.execute_query(query_shipping, params_shipping, True)

        # # Get id of billing and shipping by address and postal code
        # query_billing_id = """
        # SELECT id FROM gatchan.billing WHERE address = %s AND postal_code = %s
        # """
        # params_billing_id = (billing_address, billing_postal_code)
        # billing_id = db_manager.execute_query(query_billing_id, params_billing_id)[0][0]

        # query_shipping_id = """
        # SELECT id FROM gatchan.shipping WHERE address = %s AND postal_code = %s
        # """
        # params_shipping_id = (shipping_address, shipping_postal_code)
        # shipping_id = db_manager.execute_query(query_shipping_id, params_shipping_id)[0][0]

        params = (
            uuid4,
            name,
            contact_id,
            national_id,
            mobile_phone,
            email,
            address,
            postal_code,
            city,
            province,
            b_id,
            q_id,
        )

        db_manager.execute_query(query, params)
        return uuid4
    
    except Exception as e:
        logger("error", f"Failed to create customer: {e}")
        return None

def update_customer_(data: dict):
    """
    {
    "id": "01589d82-66ff-410f-9fd6-06b060f84a00",
    "first_name": "María Elena García Fernández 2",
    "contact_id": *********,
    "national_id": "71766538Y",
    "mobile_phone": "*********",
    "email": "<EMAIL>",
    "address": "Calle Santo Ángel 5, Bajo-C",
    "postal_code": "33403",
    "city": "Avilés",
    "province": "Asturias",
    "bank_account": "************************",
    "billing_address": "Calle Santo Ángel 5, Bajo-C",
    "billing_postal_code": "33403",
    "billing_city": "Avilés",
    "billing_province": "Asturias",
    "shipping_address": "Calle Santo Ángel 5, Bajo-C",
    "shipping_postal_code": "33403",
    "shipping_city": "Avilés",
    "shipping_province": "Asturias"
}
    """
    uuid = data.get("id")
    name = data.get("first_name")
    contact_id = data.get("contact_id")
    national_id = data.get("national_id")
    mobile_phone = data.get("mobile_phone")
    email = data.get("email")
    address = data.get("address")
    postal_code = data.get("postal_code")
    city = data.get("city")
    province = data.get("province")
    bank_account = data.get("bank_account")
    billing_address = data.get("billing_address")
    billing_postal_code = data.get("billing_postal_code")
    billing_city = data.get("billing_city")
    billing_province = data.get("billing_province")
    shipping_address = data.get("shipping_address")
    shipping_postal_code = data.get("shipping_postal_code")
    shipping_city = data.get("shipping_city")
    shipping_province = data.get("shipping_province")
    
    billing_id = None
    shipping_id = None

    # Get billing id and shipping id
    query_ids = "SELECT billing_id, shipping_id FROM gatchan.users WHERE uuid = %s"
    params_ids = (uuid,)
    result = db_manager.execute_query(query_ids, params_ids)
    if not result:
        logger("error", "Failed to retrieve billing and shipping ids")
        return None
    
    billing_id = result[0][0]
    shipping_id = result[0][1]

    # Update billing data
    query_billing = "UPDATE gatchan.billing SET bank_account = %s, address = %s, postal_code = %s, city = %s, province = %s WHERE id = %s"
    params_billing = (bank_account, billing_address, billing_postal_code, billing_city, billing_province, billing_id)

    # Update shipping data
    query_shipping = "UPDATE gatchan.shipping SET address = %s, postal_code = %s, city = %s, province = %s WHERE id = %s"
    params_shipping = (shipping_address, shipping_postal_code, shipping_city, shipping_province, shipping_id)

    # Update user data
    query_user = "UPDATE gatchan.users SET name = %s, contact_id = %s, national_id = %s, mobile_phone = %s, email = %s, address = %s, postal_code = %s, city = %s, province = %s WHERE uuid = %s"
    params_user = (name, contact_id, national_id, mobile_phone, email, address, postal_code, city, province, uuid)

    try:
        db_manager.execute_query(query_billing, params_billing)
        db_manager.execute_query(query_shipping, params_shipping)
        db_manager.execute_query(query_user, params_user)
        return True
    except Exception as e:
        print(e)


def get_customer_permanence(uuid: str):
    USER_QUERY = """
                    SELECT
                    CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END AS permanence
                    FROM
                    gatchan.users u
                    JOIN gatchan.sales s ON u.uuid = s.user_id
                    JOIN gatchan.sale_details sd ON s.id = sd.sale_id
                    JOIN gatchan.items i ON sd.item_id = i.id
                    WHERE
                    u.uuid = %s
                    AND i.recurrence_period = 99;
                """
    
    params = (uuid,)

    try:
        result = db_manager.execute_query(USER_QUERY, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    if not result:
        logger("error", "Failed to retrieve customer permanence")
        return None

    return result[0][0]

def get_emails():
    query = """
        SELECT gatchan.users.name,gatchan.users.email,gatchan.users.uuid FROM gatchan.users WHERE gatchan.users.`status` = 1
    """

    try:
        list_data = db_manager.execute_query(query)
        result = []

        for data in list_data:
            result.append({
                "name": data[0],
                "email": data[1],
                "uuid": data[2]
            })

    except Exception as e:
        logger("error", f"Database query error in get_emails(): {e}")
        return None
    
    if not result:
        logger("error", "Failed to retrieve emails")
        return None
    
    return result

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

def send_emails(subject: str, content: str, destinations: list, attachments: list = None):
    """
    Send separate emails to multiple recipients with specified content and optional attachments.

    Parameters:
    - subject (str): Email subject line.
    - content (str): Email content in HTML or plain text.
    - destinations (list): List of recipient email addresses.
    - attachments (list): List of file objects for attachments (optional).

    Returns:
    - Tuple: (Status code of the request, response message)
    """
    smtp_server = 'smtp.gmail.com'
    smtp_port = 587  # TLS port for Gmail

    email = config('EMAIL')  # Change to your Gmail email
    password = config('EMAIL_PASSWORD')  # Change to your Gmail password

    try:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(email, password)
        logger("info", "Logged in to the SMTP server")

        for destination in destinations:
            logger("info", f"Sending email to {destination['email']}")
            message = MIMEMultipart()
            message['From'] = email
            message['To'] = destination['email']
            message['Subject'] = subject

            message.attach(MIMEText(content, 'html'))

            if attachments:
                logger("info", "Attaching files to the email")
                for file in attachments:
                    file.file.seek(0)  # Asegúrate de que el archivo esté en el inicio
                    part = MIMEBase('application', "octet-stream")
                    file_content = file.file.read()  # Lee el contenido del archivo
                    part.set_payload(file_content)
                    encoders.encode_base64(part)
                    part.add_header('Content-Disposition', f"attachment; filename={file.filename}")
                    message.attach(part)
                    file.file.seek(0)  # Regresa al inicio del archivo si necesitas volver a leerlo

            server.sendmail(email, destination['email'], message.as_string())
            logger("info", f"Email sent to {destination['email']}")

        server.quit()
        return (200, "Emails sent successfully")
    except Exception as e:
        return (500, str(e))


def get_customer_notes(uuid: str):
    NOTES_QUERY = "SELECT notes FROM gatchan.users WHERE uuid = %s"
    params = (uuid,)

    # Console log the query with the params
    print(NOTES_QUERY % params)

    try:
        result = db_manager.execute_query(NOTES_QUERY, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
    if not result:
        logger("error", "Failed to retrieve customer notes")
        return None
    
    return result[0][0]

def update_customer_notes_(uuid: str, notes: str):
    UPDATE_NOTES_QUERY = "UPDATE gatchan.users SET notes = %s WHERE uuid = %s"
    
    # Check if notes is empty, if so replace it with an empty array
    notes = notes if notes else []
    
    data_to_save = {"notes": notes}
    notes_json = json.dumps(data_to_save)
    
    params = (notes_json, uuid)

    try:
        db_manager.execute_query(UPDATE_NOTES_QUERY, params)
        return True
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
def baja_user_(uuid: str):
    UPDATE_STATUS_QUERY = "UPDATE gatchan.users SET status = 0 WHERE uuid = %s"
    params = (uuid,)

    try:
        db_manager.execute_query(UPDATE_STATUS_QUERY, params)
        return True
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None