from core.firewall.FirewallConnection import api_responses, enqueue_firewall_request, enqueue_connection_request
from core.logs import logger, saveBackup
import threading
import time
from datetime import datetime, timed<PERSON>ta

def create_schedule(schedule, schedule_name):
    """
    Create a new schedule on the firewall using the provided schedule data.

    Args:
    schedule (dict): Dictionary containing the schedule parameters.
    schedule_name (str): Name for the new schedule.

    Returns:
    dict: The modified schedule in JSON format ready to be sent to the firewall.
    """

    # Mapeo de los días de la semana
    days_map = {
        "lunes": "mon",
        "martes": "tue",
        "miercoles": "wed",
        "jueves": "thu",
        "viernes": "fri",
        "sabado": "sat",
        "domingo": "sun"
    }

    json_scheduler = {
        "scheduler": {
            "schedule": [
                {
                    "name": None,
                    "occurs": {
                        "recurring": {
                            "recurring": [

                            ]
                        }
                    }
                }
            ]
        }
    }

    # Inicializar días seleccionados
    selected_days = {day: False for day in days_map.values()}

    # E<PERSON>cer días seleccionados
    if schedule.get("checkAllDays"):
        selected_days = {day: True for day in days_map.values()}
    else:
        for day, value in schedule["dias"].items():
            if value and day in days_map:
                selected_days[days_map[day]] = True

    start_time = schedule.get('horaInicio', "00:00")
    end_time = schedule.get('horaFin', "23:59")

    # Limpiar los horarios existentes
    json_scheduler["scheduler"]["schedule"][0]["occurs"]["recurring"]["recurring"] = []

    # Verificar si el horario cruza la medianoche
    if start_time > end_time:
        # Horario cruza la medianoche, dividir en dos
        first_schedule = selected_days.copy()
        first_schedule.update({"start": start_time, "end": "24:00"})

        second_schedule = selected_days.copy()
        # Ajustar días para el segundo horario
        second_schedule = {day: (selected_days[prev_day] if prev_day else False) 
                           for day, prev_day in zip(days_map.values(), [None] + list(days_map.values())[:-1])}
        second_schedule.update({"start": "00:00", "end": end_time})

        json_scheduler["scheduler"]["schedule"][0]["occurs"]["recurring"]["recurring"].append(first_schedule)
        json_scheduler["scheduler"]["schedule"][0]["occurs"]["recurring"]["recurring"].append(second_schedule)
    else:
        # Horario no cruza la medianoche
        new_schedule = selected_days.copy()
        new_schedule.update({"start": start_time, "end": end_time})

        json_scheduler["scheduler"]["schedule"][0]["occurs"]["recurring"]["recurring"].append(new_schedule)

    json_scheduler["scheduler"]["schedule"][0]["name"] = str(schedule_name)

    return json_scheduler

def fill_uncovered_schedule(existing_schedules, schedule_name):
    # Define todos los días de la semana
    all_days = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]

    # Inicializa un diccionario para rastrear las horas cubiertas en cada día
    covered_hours = {day: [] for day in all_days}

    # Analiza los horarios existentes para marcar las horas cubiertas
    for schedule in existing_schedules:
        for rule in schedule["scheduler"]["schedule"]:
            for recurring in rule["occurs"]["recurring"]["recurring"]:
                start_hour = int(recurring["start"].split(":")[0])
                end_hour = int(recurring["end"].split(":")[0])
                for day in all_days:
                    if recurring.get(day, False):
                        covered_hours[day] += list(range(start_hour, end_hour))

    # Genera el nuevo horario llenando las horas no cubiertas
    new_schedule = {
        "scheduler": {
            "schedule": [
                {
                    "name": schedule_name,
                    "occurs": {
                        "recurring": {
                            "recurring": []
                        }
                    }
                }
            ]
        }
    }

    for day in all_days:
        # Encuentra las horas no cubiertas para cada día
        uncovered_hours = [hour for hour in range(24) if hour not in covered_hours[day]]
        if uncovered_hours:
            # Asume que quieres cubrir todo el día si no hay horas cubiertas
            start_time = f"{min(uncovered_hours):02d}:00"
            end_time = f"{max(uncovered_hours) + 1:02d}:00"
            if end_time == "24:00":
                end_time = "23:59"
            new_schedule["scheduler"]["schedule"][0]["occurs"]["recurring"]["recurring"].append({
                day: True,
                "start": start_time,
                "end": end_time
            })

    return new_schedule

def get_days_from_schedule(schedule):
    days_map_inverse = {
        "mon": "lunes", "tue": "martes", "wed": "miercoles",
        "thu": "jueves", "fri": "viernes", "sat": "sabado", "sun": "domingo"
    }

    day_status = {day: {'selected': False, 'start': None, 'end': None} for day in days_map_inverse.values()}

    if 'schedule' in schedule:
        for entry in schedule['schedule']:
            if 'occurs' in entry and 'recurring' in entry['occurs']:
                for recur_entry in entry['occurs']['recurring']['recurring']:
                    start_time = recur_entry.get('start')
                    end_time = recur_entry.get('end')
                    for day_key, is_selected in recur_entry.items():
                        if day_key in days_map_inverse and is_selected:
                            current_day = days_map_inverse[day_key]
                            day_status[current_day]['selected'] = True
                            if not day_status[current_day]['start'] or (start_time < day_status[current_day]['start']):
                                day_status[current_day]['start'] = start_time
                            if not day_status[current_day]['end'] or (end_time > day_status[current_day]['end']):
                                day_status[current_day]['end'] = end_time
                            # Manejar caso en que el horario cruza la medianoche
                            if start_time and end_time and start_time > end_time:
                                # Se asume que el horario cruza la medianoche
                                day_status[current_day]['end'] = f"{end_time} (siguiente día)"

    return day_status




def create_address_object(ip, name, zone):
    """
    Create a new address object on the firewall.

    Args:
    ip (str): The IP address for the new address object.
    name (str): The name of the new address object.
    zone (str): The zone where the new address object will be applied.

    This function updates the JSON structure used for creating address objects
    by setting the provided IP, name, and zone.
    """

    json_address_objects = {
        "address_objects": [
            {
                "ipv4": {
                    "name": None,
                    "zone": None,
                    "host": {
                        "ip": None
                    }
                }
            }
        ]
    }

    json_address_objects['address_objects'][0]['ipv4']['name'] = str(name)
    json_address_objects['address_objects'][0]['ipv4']['host']['ip'] = str(ip)
    json_address_objects['address_objects'][0]['ipv4']['zone'] = str(zone)

    return json_address_objects

def create_rule(name, name_address, name_schedule, comment, service):
    """
    Create a new rule on the firewall.

    Args:
    name (str): The name of the new rule.
    name_address (str): The name of the address object associated with the rule.
    name_schedule (str): The name of the schedule associated with the rule.
    comment (str): A comment or description for the rule.
    service (str): The service (like HTTP, FTP, etc.) associated with the rule.

    This function updates the JSON structure used for creating access rules
    by setting the provided rule name, address object name, schedule name,
    comment, and service.
    """
    json_access_rules = {
        "access_rules": [
            {
                "ipv4": {
                    "name": None,
                    "from": None,
                    "to": "WAN",
                    "action": "deny",
                    "comment": None,
                    "source": {
                        "address": {
                            "name": None
                        },
                        "port": {
                            "any": True
                        }
                    },
                    "service": {
                        "any": True
                    },
                    "destination": {
                        "address": {
                            "any": True
                        }
                    },
                    "schedule": {
                        "name": None
                    },
                    "users": {
                        "included": {
                            "all": True
                        },
                        "excluded": {
                            "none": True
                        }
                    }
                }
            }
        ]
    }

    json_access_rules['access_rules'][0]['ipv4']['name'] = str(name)
    json_access_rules['access_rules'][0]['ipv4']['schedule']['name'] = str(name_schedule)
    json_access_rules['access_rules'][0]['ipv4']['comment'] = str(comment)
    json_access_rules['access_rules'][0]['ipv4']['source']['address']['name'] = str(name_address)
    json_access_rules['access_rules'][0]['ipv4']['from'] = str(service)

    return json_access_rules

def save_object(type):
    completed = threading.Event()
    enqueue_connection_request(login=True)
    timeout = timedelta(seconds=10)
    start_time = datetime.now()

    # Define las claves y estructura para almacenar respuestas
    keys = {
        'schedule': 'schedules',
        'address-objects': 'address-objects/ipv4',
        'access-rules': 'access-rules/ipv4'
    }
    responses = {key: None for key in keys.values()}

    # Envía solicitudes
    if type == 'all':
        for obj_type, key in keys.items():
            enqueue_firewall_request(url=key, method='GET')
    elif type in keys:
        key = keys[type]
        enqueue_firewall_request(url=key, method='GET')
    else:
        logger("error", f"Invalid object type: {type}")
        completed.wait(timeout=5)
        enqueue_connection_request(logout=True)
        return

    # Espera y recopila respuestas
    while datetime.now() - start_time < timeout and not all(responses.values()):
        for key in keys.values():
            if responses[key] is None and key in api_responses:
                responses[key] = api_responses[key]
        time.sleep(0.5)

    # Comprobación de las respuestas
    if not all(response for response in responses.values()):
        logger("error", "Did not receive all responses within the timeout period.")
        enqueue_connection_request(logout=True)
        return {"error": "Timeout", "responses": responses}

    # Almacenamiento de respuestas y desconexión
    for obj_type, key in keys.items():
        if key in responses and responses[key]:
            saveBackup(responses[key], obj_type)  # Usando 'obj_type' para 'typeBackup'
        else:
            logger("error", f"Response for {key} not found in responses")

    enqueue_connection_request(logout=True)
    return {"success": "All objects processed", "responses": responses}
