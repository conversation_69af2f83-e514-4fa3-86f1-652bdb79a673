version: '3.8'

networks:
  gatchan_network:
    driver: bridge

services:
  db:
    build:
      context: .
      dockerfile: Dockerfile.mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: gatchan_app
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3307:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - gatchan_network

  api:
    build:
      context: .
      dockerfile: Dockerfile
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload --workers 10
    volumes:  
      - .:/usr/src/app
      - ../statistics:/usr/src/app/statistics
    depends_on:
      - db
    # environment:
      # - DB_HOST=db
      # - DB_USER=root
      # - DB_PASSWORD=rootpassword
      # - DB_NAME=gatchan_app
    ports:
      - "8000:8000"
    networks:
      - gatchan_network

  # react-app:
  #   build:
  #     context: ../Gathan-App
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3000:80"
  #   depends_on:
  #     - api
  #   networks:
  #     - gatchan_network
  #   volumes:
  #     - ./deploy/nginx.conf:/etc/nginx/conf.d/default.conf

  # react-crm:
  #   build:
  #     context: ../Gatchan-CRM
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3001:80"
  #   depends_on:
  #     - api
  #   networks:
  #     - gatchan_network
  #   volumes:
  #     - ./deploy/nginx.conf:/etc/nginx/conf.d/default.conf

  # mongodb:
  #   image: mongo:latest
  #   restart: always
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: root
  #     MONGO_INITDB_ROOT_PASSWORD: password
  #   ports:
  #     - 27018:27017
  #   networks:
  #     - gatchan_network
  #   volumes:
  #     - mongodb_data:/data/db

volumes:
  db_data:
  stats_data:
  mongodb_data: