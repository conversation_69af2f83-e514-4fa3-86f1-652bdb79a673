import queue
import threading
import uuid
from datetime import datetime, timedelta
from dotenv import load_dotenv
import requests
from requests.auth import HTTPDigestAuth
import json
import os
import time
from core.logs import logger

# Configuración centralizada
load_dotenv()
FIREWALL_USERNAME = os.getenv("FIREWALL_USERNAME")
FIREWALL_PASSWORD = os.getenv("FIREWALL_PASSWORD")
FIREWALL_URL = os.getenv("FIREWALL_URL")

# Cola para manejar las tareas
task_queue = queue.Queue()

# Cola para manejar conexiones en espera
waiting_connections = queue.Queue()

# Variables para el control de la sesión
current_session_token = None
session_timeout_minutes = 2
last_session_activity = None

# Estructura de datos para almacenar respuestas de la API
api_responses = {}

# Encabezados para las solicitudes HTTP
headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Accept-Encoding': 'application/json',
    'Charset': 'UTF-8'
}

def is_session_active():
    global last_session_activity
    if not current_session_token or not last_session_activity:
        return False
    if datetime.now() - last_session_activity > timedelta(minutes=session_timeout_minutes):
        end_session()
        return False
    return True

def start_session():
    global current_session_token, last_session_activity
    current_session_token = str(uuid.uuid4())
    last_session_activity = datetime.now()

def end_session():
    global current_session_token, last_session_activity, api_responses
    current_session_token = None
    last_session_activity = None
    # api_responses = {}

def cleanup_api_responses():
    max_age = timedelta(minutes=5)  # Por ejemplo, 5 minutos
    while True:
        now = datetime.now()
        with threading.Lock():  # Asegura el acceso seguro al diccionario
            for request_id in list(api_responses.keys()):
                response_time = api_responses[request_id]['timestamp']
                if now - response_time > max_age:
                    del api_responses[request_id]
        logger("firewall", f"API responses cleaned up. {len(api_responses)} responses remaining")
        time.sleep(60 * 5) # Espera 5 minutos antes de volver a ejecutar la limpieza

def connectToFirewall(login=False, logout=False):
    global last_session_activity
    if login:
        if is_session_active():
            # En lugar de rechazar, poner en espera
            waiting_connections.put({'login': login, 'logout': logout})
            logger("firewall", "Login request enqueued")
            return None  # Puedes devolver un código de error aquí
        else:
            start_session()
            authinfo = HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD)
            try:
                r = requests.post(FIREWALL_URL + 'auth', auth=authinfo, headers=headers, data=json.dumps({"override": True}), verify=False)
                if r.status_code == 200:
                    logger("firewall", "Login successful")
                    return r.json()  # Devuelve los datos de autenticación
                else:
                    logger("error_firewall", f"Login failed with status code {r.status_code} and response: {r.text}")
                    return None  # Puedes devolver un código de error aquí
            except Exception as e:
                logger("error_firewall", f"Login failed with exception: {str(e)}")
                return None  # Puedes devolver un código de error aquí
            finally:
                last_session_activity = datetime.now()

    if logout:
        authinfo = HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD)
        try:
            r = requests.delete(FIREWALL_URL + 'auth', auth=authinfo, headers=headers, verify=False)
            if r.status_code == 200:
                logger("firewall", "Logout successful")
                end_session()
                process_waiting_connections()
                return r.json()  # Devuelve los datos de cierre de sesión
            else:
                logger("error_firewall", f"Logout failed with status code {r.status_code} and response: {r.text}")
                return None  # Puedes devolver un código de error aquí
        except Exception as e:
            logger("error_firewall", f"Logout failed with exception: {str(e)}")
            return None  # Puedes devolver un código de error aquí

def sendToFirewall(url, method, data=None, request_id=None):
    if not is_session_active():
        logger("error_firewall", "Session is not active")
        return None  # Puedes devolver un código de error aquí

    global last_session_activity
    last_session_activity = datetime.now()

    try:
        full_url = FIREWALL_URL + url
        response = None

        if method.upper() == 'GET':
            logger("firewall", f"Sending request to {full_url}" + (f" with request ID {request_id}" if request_id else "NULL"))
            response = requests.get(full_url, auth=HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD), headers=headers, verify=False)
        elif method.upper() == 'POST':
            logger("firewall", f"Sending data to {full_url}: {data}")
            response = requests.post(full_url, auth=HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD), headers=headers, data=data, verify=False)
        elif method.upper() == 'PUT':
            logger("firewall", f"Sending data to {full_url}: {data}")
            response = requests.put(full_url, auth=HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD), headers=headers, data=data, verify=False)
        elif method.upper() == 'DELETE':
            logger("firewall", f"Sending request to {full_url}" + (f" with request ID {request_id}" if request_id else "NULL"))
            response = requests.delete(full_url, auth=HTTPDigestAuth(FIREWALL_USERNAME, FIREWALL_PASSWORD), headers=headers, verify=False)
        if response and response.status_code == 200:
            # Almacena la respuesta con el ID
            # api_responses[request_id] = {'status_code': response.status_code, 'data': response.json() if response.status_code == 200 else response.text}
            api_responses[request_id] = {
                'status_code': response.status_code, 
                'data': response.json() if response.status_code == 200 else response.text,
                'timestamp': datetime.now()
            }
            # logger("info", api_responses)
            logger("firewall", f"Request to {url} successful with method {method} and status code {response.status_code}")
            return response.json()
        else:
            # api_responses[request_id] = {'status_code': response.status_code, 'data': response.json() if response.status_code == 200 else response.text}
            api_responses[request_id] = {
                'status_code': response.status_code, 
                'data': response.json() if response.status_code == 200 else response.text,
                'timestamp': datetime.now()
            }
            logger("error_firewall", f"Request to {url} failed with status code {response.status_code} and response: {response.json()}")
            return None  # Puedes devolver un código de error aquí
    except Exception as e:
        logger("error_firewall", f"Request to {url} failed with exception: {str(e)}")
        return None  # Puedes devolver un código de error aquí

def process_waiting_connections():
    if not waiting_connections.empty():
        next_connection = waiting_connections.get()
        connectToFirewall(**next_connection)

def task_worker():
    while True:
        task_type, task_data = task_queue.get()
        try:
            if task_type == 'connect':
                connectToFirewall(**task_data)
            elif task_type == 'request':
                sendToFirewall(**task_data)
        finally:
            task_queue.task_done()

def convert_bools_for_json(data):
    def convert(data):
        if isinstance(data, dict):
            return {k: convert(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [convert(element) for element in data]
        elif isinstance(data, bool):
            return data  # No cambia el valor booleano, lo mantiene tal como está
        else:
            return data

    # Llama a la función de conversión
    return convert(data)


def enqueue_connection_request(login=False, logout=False):
    task_queue.put(('connect', {'login': login, 'logout': logout}))

def enqueue_firewall_request(url, method, data=None):
    request_id = str(uuid.uuid4())
    corrected_data = convert_bools_for_json(data)
    json_data = json.dumps(corrected_data)  # Convierte la estructura de datos corregida a JSON
    task_queue.put(('request', {'url': url, 'method': method, 'data': json_data, 'request_id': request_id}))
    return request_id

# Iniciar hilo de limpieza
threading.Thread(target=cleanup_api_responses, daemon=True).start()
threading.Thread(target=task_worker, daemon=True).start()