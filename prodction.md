# 🚀 Deploying Gatchan with Docker Compose! 🐳

## Step 1: Install Docker 🐋

First, make sure to install Dock<PERSON> and Docker Compose. You can follow the instructions for Ubuntu 22.04 [here](https://www.digitalocean.com/community/tutorials/how-to-install-and-use-docker-on-ubuntu-22-04) and [here](https://www.digitalocean.com/community/tutorials/how-to-install-and-use-docker-compose-on-ubuntu-22-04).

## Step 2: Prepare Projects 🛠️

Before building the React app, update the `.env` and `.env.production` files with the new path. Run 'npm run build' for 'gatchan-app' and ensure no build is done for the API.

Check the 'docker-compose.yml' in 'GatchanApi'; verify database credentials in both API and database services.

## Step 3: Transfer Projects to the Machine 📁

Ensure project folders are in the same path and share the same location as the 'statistics' folder at '/home/<USER>'

## Step 4: Build Projects 🏗️

To launch the projects, first run 'docker compose up --build' in the API folder. Afterward, use 'docker compose up.'

- If there are API changes, the same command applies.
- For React changes, build React, transfer, and then 'docker compose up --build.'

You can test if it works using the machine's IP and the specified ports (defined in 'docker-compose.yml' in 'gatchanapi').

## Step 5: Configure Domain 🌐

Navigate to '/opt/bitnami/apache/conf/bitnami,' edit 'bitnami-ssl.conf,' and configure domain routes locally. Example:

```apache
ProxyPass /api/v1 http://127.0.0.1:8001
ProxyPassReverse /api/v1 http://127.0.0.1:8001
ProxyPass / http://127.0.0.1:3000/
ProxyPassReverse / http://127.0.0.1:3000/
```

## Restart Services 🔁

If needed, restart Bitnami services:

```bash
sudo service bitnami restart
```

Happy Deploying! 🚀✨