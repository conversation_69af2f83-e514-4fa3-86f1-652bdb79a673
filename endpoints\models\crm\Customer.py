from pydantic import BaseModel

class Customer(BaseModel):
    """
    Pydantic model for customer data.
    """
    id: int
    first_name: str
    contact_id: str
    national_id: str
    mobile_phone: str
    email: str
    address: str
    postal_code: str
    city: str
    province: str
    bank_account: str
    billing_address: str
    billing_postal_code: str
    billing_city: str
    billing_province: str
    shipping_address: str
    shipping_postal_code: str
    shipping_city: str
    shipping_province: str