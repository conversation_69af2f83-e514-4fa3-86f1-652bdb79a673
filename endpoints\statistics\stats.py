import os
import json
from typing import List, Dict
import datetime

# Current path and ../statistics
# stats_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..","..", "..", "statistics")
stats_path = os.path.dirname(os.path.abspath(__file__))
stats_path = stats_path.replace("endpoints/statistics", "statistics")

def yesterday() -> str:
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    # transform to string YYYYMMDD
    yesterday = yesterday.strftime("%Y%m%d")
    return yesterday

def get_date_stats(id: str, date: str):
    file_name = date + "_client_" + id + "_statistics.json"
    file_path = os.path.join(stats_path, date, file_name)

    # Check if file exists
    if not os.path.isfile(file_path):
        print("Path: " + file_path)
        return json.dumps({"error": "File not found"}, indent=4)

    # Try to open and read file
    try:
        with open(file_path, "r") as file:
            file_content_apps = extract_mins_json(file.read(), "mins_apps")
            file_content_RRSS = extract_mins_json(file.read(), "mins_RRSS")
            file_content = sum_jsons([file_content_apps, file_content_RRSS])
            file_content = json.dumps(file_content, indent=4)
            return file_content
            # return file_content_apps
    except Exception as e:
        return json.dumps({"error": str(e)}, indent=4)

def get_last_month_stats(id: str):
    # Make a list of the last 30 days
    today = datetime.date.today()
    today = today - datetime.timedelta(days=1)
    last_month = today - datetime.timedelta(days=30)
    # transform to string YYYYMMDD
    last_month = last_month.strftime("%Y%m%d")
    # Make a list of the last 30 days
    last_month_list = []
    for i in range(30):
        last_month_list.append(last_month)
        last_month = datetime.datetime.strptime(last_month, "%Y%m%d")
        last_month = last_month + datetime.timedelta(days=1)
        last_month = last_month.strftime("%Y%m%d")

    files_list = []
    for date in last_month_list:
        file_name = date + "_client_" + id + "_statistics.json"
        # Open file
        # If file exists, add it to the list
        if os.path.isfile(f"{stats_path}/{date}/{file_name}"):
            file = open(f"{stats_path}/{date}/{file_name}", "r")
            files_list.append(file.read())

    # Sum the stats
    summed_stats_apps = sum_stats_category(files_list, "mins_apps")
    summed_stats_RRSS = sum_stats_category(files_list, "mins_RRSS")

    total_sum = sum_jsons([summed_stats_apps, summed_stats_RRSS])
    total_sum = json.dumps(total_sum, indent=4)
    return total_sum

    # Convert the result to a JSON formatted string
    summed_stats_apps = json.dumps(summed_stats_apps, indent=4)

    return summed_stats_apps

def extract_mins_json(json_data: str, category: str) -> str:
    """
    Extracts the 'category' statistics from a JSON formatted string.

    :param json_data: A JSON formatted string, containing statistics data
    :param category: The category to extract from the JSON data
    :return: A JSON formatted string, containing the 'category' statistics
    """
    # Parse the JSON data
    try:
        data = json.loads(json_data)
    except json.JSONDecodeError as e:
        print("JSON Decode Error:", str(e))
        return json.dumps({"error": str(e)}, indent=4)
    
    # Check if required keys exist in the data
    if "statistics" not in data or "items" not in data["statistics"]:
        print("Missing required keys in the input data")
        return json.dumps({"error": "Missing required keys in the input data"}, indent=4)
    
    # Initialize an empty dictionary to store the results
    result = {}
    
    # Iterate through the items in the JSON data
    for item in data["statistics"]["items"]:
        item_product_id = item.get("item_product_id")
        mins_apps = item.get(category, []) # mins_apps / mins_RRSS
        
        # Convert the list of dictionaries to a single dictionary
        apps_dict = {list(app.keys())[0]: list(app.values())[0] for app in mins_apps}
        
        # Add the result to the dictionary
        result[str(item_product_id)] = apps_dict
    
    # Convert the result to a JSON formatted string
    result_json = json.dumps(result, indent=4)
    
    return result_json

def sum_stats_category(json_list: List[str], category: str) -> Dict[str, Dict[str, int]]:
    """
    Sums the 'mins_apps' statistics from a list of JSON formatted strings.
    
    :param json_list: A list of JSON formatted strings, each containing statistics data
    :return: A dictionary with 'mins_apps' data summed across all inputs, separated by 'item_product_id'
    """
    # Initialize an empty dictionary to store the summed results
    summed_results = {}
    
    for json_data in json_list:
        # Parse the JSON data
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            print("JSON Decode Error:", str(e))
            continue
        
        # Check if required keys exist in the data
        if "statistics" not in data or "items" not in data["statistics"]:
            print("Missing required keys in the input data")
            continue
        
        # Iterate through the items in the JSON data
        for item in data["statistics"]["items"]:
            item_product_id = item.get("item_product_id")
            mins_apps = item.get(category, [])
            
            # Convert the list of dictionaries to a single dictionary
            apps_dict = {list(app.keys())[0]: list(app.values())[0] for app in mins_apps}
            
            # Sum the results in the dictionary
            if item_product_id not in summed_results:
                summed_results[item_product_id] = apps_dict
            else:
                for app, time in apps_dict.items():
                    if app in summed_results[item_product_id]:
                        summed_results[item_product_id][app] += time
                    else:
                        summed_results[item_product_id][app] = time
    
    return summed_results


import json
from typing import List, Dict


from typing import List, Dict, Union

def sum_jsons(data_list: List[Union[str, Dict]]) -> Dict[int, Dict[str, int]]:
    """
    Sums the values for each numeric key across multiple JSON strings or dictionary objects.
    Skips any entries that are not valid JSON or have no data.

    :param data_list: A list of JSON formatted strings or dictionaries
    :return: A dictionary with aggregated values for each numeric key
    """
    summed_values = {}

    for entry in data_list:
        # Attempt to load the entry if it's a string, otherwise use it directly if it's a dict
        if isinstance(entry, str):
            try:
                data = json.loads(entry)
            except json.JSONDecodeError:
                # Skip this entry if it's not valid JSON
                continue
        elif isinstance(entry, dict):
            data = entry
        else:
            # Skip this entry if it's neither a dict nor a string
            continue

        # Process the data
        for key, values in data.items():
            if isinstance(key, int) or str(key).isdigit():
                if key not in summed_values:
                    summed_values[key] = {}

                for sub_key, value in values.items():
                    summed_values[key][sub_key] = summed_values[key].get(sub_key, 0) + value

    return summed_values





