from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from endpoints.auth.login import decode_token, get_user_by_uuid
from datetime import datetime, timedelta
from jwt.exceptions import InvalidSignatureError, ExpiredSignatureError, Decode<PERSON>rror
from decouple import config
from mailjet_rest import Client

security = HTTPBearer()

def requires_auth(request: Request, token: HTTPAuthorizationCredentials = Depends(security)):
    """
    Validates the JWT token in the authorization header of the request.

    This function decodes the JWT token, checks its validity, expiration, and the existence
    of required claims such as "UUID". If the validation fails, it raises appropriate HTTP
    exceptions with corresponding status codes.

    Parameters:
    - request (Request): The request object, not directly used but required by FastAPI.
    - token (HTTPAuthorizationCredentials, optional): Token credentials obtained from the request header.
      It defaults to the dependency that extracts the token using FastAPI's security utilities.

    Returns:
    - user: The validated user object obtained from the decoded token UUID.

    Raises:
    - HTTPException: An error with a status code indicating the nature of the authentication failure.

    Exceptions are raised for:
    - Missing token.
    - Token without "UUID".
    - Expired token.
    - Invalid token signature.
    - Decoding failure.
    - Invalid user from UUID.
    - Other unexpected errors during validation.
    """
    token_str = token.credentials
    
    if not token_str:
        raise HTTPException(status_code=401, detail="Token not provided")
    
    try:
        decoded = decode_token(token_str)
        
        # Verifica que el token contenga "UUID"
        if "UUID" not in decoded:
            raise HTTPException(status_code=403, detail="Token does not contain UUID")
        
        # Verifica la fecha de vencimiento
        if "exp" in decoded and datetime.utcfromtimestamp(decoded["exp"]) <= datetime.utcnow():
            raise ExpiredSignatureError()  # Leveraging the exception you imported
        
        # Verifica la validez del usuario
        user = get_user_by_uuid(decoded["UUID"])
        if not user:
            raise HTTPException(status_code=403, detail="User not valid")

    except InvalidSignatureError:
        raise HTTPException(status_code=403, detail="Invalid token signature")
    except ExpiredSignatureError:
        raise HTTPException(status_code=403, detail="Token has expired")
    except DecodeError:
        raise HTTPException(status_code=403, detail="Token could not be decoded")
    except Exception as e:  # Generic exception to catch other unforeseen errors
        raise HTTPException(status_code=403, detail=f"Token not valid: {str(e)}")
    
    return user

def send_email(email: str, subject: str, content: str, from_email: str = "Gatchan <<EMAIL>>"):
    """
    Send an email to a recipient with specified content.

    Parameters:
    - email (str): Recipient email address.
    - subject (str): Email subject line.
    - content (str): Email content in HTML or plain text.
    - from_email (str): Sender's email address (default is "Gatchan <<EMAIL>>").

    Returns:
    - Tuple: (Status code of the request, JSON response from Mailjet API)
    """
    api_key = config('MAILJET_API_KEY')
    api_secret = config('MAILJET_API_SECRET')
    mailjet = Client(auth=(api_key, api_secret), version='v3.1')

    data = {
        'Messages': [
            {
                "From": {
                    "Email": from_email.split('<')[1].strip('>'),  # Extrae solo el correo electrónico
                    "Name": from_email.split('<')[0].strip()
                },
                "To": [
                    {
                        "Email": email
                    }
                ],
                "Subject": subject,
                "TextPart": content,  # Asumiendo que content es texto plano
                # "HTMLPart": content,  # Descomentar si content es HTML
            }
        ]
    }
    result = mailjet.send.create(data=data)
    return result.status_code, result.json()
