import pymysql
from decouple import config
from core.logs import logger

class DatabaseManager:
    def __init__(self, host, user, password, db):
        self.host = host
        self.user = user
        self.password = password
        self.db = db
        self.conn = None

    def _connect(self):
        if self.conn is None or not self.conn.open:
            self.conn = pymysql.connect(host=self.host,
                                        user=self.user,
                                        password=self.password,
                                        db=self.db)

    def _close(self):
        if self.conn and self.conn.open:
            self.conn.close()
            self.conn = None

    def execute_query(self, query, params=None, lastrowid=False):
        """
        Executes a SQL query on the database and returns its results.

        Args:
            query (str): SQL query to be executed.
            params (tuple, optional): Parameters for the SQL query. Defaults to None.
            lastrowid (bool, optional): If True, returns the last inserted row ID for insert queries. Defaults to False.

        Returns:
            list | int: List of tuples containing query results, or last inserted row ID if lastrowid is True.

        Note:
            Logs an error message if the query execution fails.
        """
        self._connect()
        result = None
        try:
            with self.conn.cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if lastrowid:
                    result = cursor.lastrowid
                else:
                    result = cursor.fetchall()

            self.conn.commit()
        except Exception as e:
            logger("error", f"Error al ejecutar la consulta: {e} - {query}")
        finally:
            self._close()
        return result


# Configuración de la base de datos desde .env
db_config = {
    'host': config('DB_HOST'),
    'user': config('DB_USER'),
    'password': config('DB_PASSWORD'),
    'db': config('DB_NAME')
}

db_manager = DatabaseManager(**db_config)
