import json
import os


class User:
    def __init__(self, _id: str, system_description: str = "_"):
        self.id = _id
        self.chat_history = [{"role": "system", "content": system_description}]

        self.load_chat_history()

    def __repr__(self):
        return "User(id='%s')" % self.id

    def add_chat(self, chat):
        self.chat_history.append(chat)
        self.save_chat_history()

    def get_chat_history(self):
        return self.chat_history

    def get_id(self):
        return self.id

    def load_chat_history(self) -> None:
        print(f"Loading chat history for {self.id}")
        chat_file_path: str = f"chat/{self.id}.json"
        if os.path.exists(chat_file_path):
            with open(chat_file_path, "r") as chat_file:
                self.chat_history = json.load(chat_file)
        else:
            if not os.path.exists("chat"):
                os.mkdir("chat")
            print(f"Creating chat history for {self.id}")
            with open(chat_file_path, "w") as chat_file:
                json.dump(self.chat_history, chat_file)


    def save_chat_history(self):
        print(f"Saving chat history for {self.id}")
        new_chat: list = self.chat_history

        # Check if there are at least 5 elements in the list
        if len(new_chat) >= 5:
            # Keep the first element and the last four elements
            new_data = [new_chat[0]] + new_chat[-4:]
        else:
            # If there are fewer than 5 elements, keep all elements
            new_data = new_chat

        # Save the chat history to a JSON file
        with open(f"chat/{self.id}.json", "w") as f:
            json.dump(new_data, f)
