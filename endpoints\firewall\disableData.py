import asyncio
from core.firewall.FirewallConnection import api_responses, enqueue_firewall_request, enqueue_connection_request
from db.connection import db_manager
from core.logs import logger
from datetime import datetime, timedelta
import json

def process_responses(responses, keys):
    processed_data = {}
    for key in keys:
        if key in responses:
            processed_data[key] = responses[key]
        else:
            logger("error", f"Response for {key} not found")
    return processed_data

def find_ipv4_name(address_object):
    # Verifica si el objeto es un diccionario y busca directamente 'ipv4' y 'name'
    if isinstance(address_object, dict):
        if 'ipv4' in address_object and 'name' in address_object['ipv4']:
            return address_object['ipv4']['name']
        for key, value in address_object.items():
            if isinstance(value, dict) or isinstance(value, list):
                result = find_ipv4_name(value)  # Llamada recursiva para buscar en el siguiente nivel
                if result is not None:
                    return result
    # Si el objeto es una lista, itera a través de sus elementos
    elif isinstance(address_object, list):
        for item in address_object:
            result = find_ipv4_name(item)  # Llamada recursiva para cada elemento de la lista
            if result is not None:
                return result
    # Retorna None si no se encuentra ninguna coincidencia
    return None

async def get_all_objects():
    num_requests = 3
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Enqueue a request to connect to the firewall
    enqueue_connection_request(login=True)

    # Enqueue requests to get various objects and store their IDs
    request_ids = {
        'schedules': enqueue_firewall_request(url='schedules', method='GET'),
        'address-objects/ipv4': enqueue_firewall_request(url='address-objects/ipv4', method='GET'),
        'access-rules/ipv4': enqueue_firewall_request(url='access-rules/ipv4', method='GET')
    }

    logger("info", "All requests enqueued for getting objects")

    # Initialize a dictionary to store the responses
    responses = {key: None for key in request_ids.keys()}

    # Espera hasta que todas las respuestas hayan sido recibidas o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", "Timeout waiting for responses from the firewall while getting objects")
            break

        all_responses_received = all(request_id in api_responses for request_id in request_ids.values())
        if all_responses_received:
            logger("info", "All responses received from getting objects from the firewall")
            
            # Recuperar y eliminar las respuestas de api_responses
            for key, request_id in request_ids.items():
                response = api_responses.pop(request_id, None)
                if response and response['status_code'] == 200:
                    responses[key] = response['data']
                else:
                    logger("error", f"Error in response for {key}: {response}")

            break

        await asyncio.sleep(0.5)  # Espera un breve periodo antes de verificar de nuevo

    # Procesa las respuestas aquí utilizando la función process_responses
    keys_to_process = ['schedules', 'address-objects/ipv4', 'access-rules/ipv4']
    processed_responses = process_responses(responses, keys_to_process)

    # Enqueue a request to disconnect from the firewall
    enqueue_connection_request(logout=True)

    if len(processed_responses) < num_requests:
        logger("error", "Failed to get all objects from the firewall")

    logger("info", "Logout request enqueued after getting objects")
    return processed_responses


def getObjectsByUnique_id(data, unique_id):
    result = [] 
    schedules_data = {}  
    address_objects_data = {}  
    list_rules = data.get('access-rules/ipv4', {}).get('access_rules', [])

    try:
        # Registro de inicio
        logger("start", f"string found in access_rules by unique_id: {unique_id}")

        # Búsqueda de reglas de acceso
        for rule in list_rules:
            try:
                if rule.get('ipv4', {}).get('name', '').startswith("Redapp_" + str(unique_id)):
                    result.append(rule)
            except KeyError as ke:
                logger("error", f"Error al acceder a clave en access_rules: {ke}")

        # Búsqueda de schedules relacionados
        if 'schedules' in data and 'scheduler' in data['schedules'] and 'schedule' in data['schedules']['scheduler']:
            for rule in result:
                try:
                    rule_schedule_name = rule.get('ipv4', {}).get('schedule', {}).get('name', '')
                    if rule_schedule_name:
                        for schedule in data['schedules']["scheduler"]["schedule"]:
                            if schedule.get('name') == rule_schedule_name:
                                schedules_data[schedule['name']] = schedule
                except KeyError as ke:
                    logger("error", f"Error al acceder a clave en schedules: {ke}")

        # Búsqueda de address-objects filtrados por unique_id en su nombre
        if 'address-objects/ipv4' in data and 'address_objects' in data['address-objects/ipv4']:
            address_objects = data['address-objects/ipv4']["address_objects"]
            for address in address_objects:
                try:
                    address_name = address.get('ipv4', {}).get('name', '')
                    # Filtrar por aquellos que contienen el unique_id en el formato especificado
                    if f"address_object_{unique_id}" in address_name:
                        # Almacenar los address objects filtrados
                        address_objects_data[address_name] = address
                except KeyError as ke:
                    logger("error", f"Error al acceder a clave en address_objects: {ke}")

        # Devolvemos un diccionario con los resultados organizados
        return {
            "access_rules": result,
            "schedules": schedules_data,
            "address_objects": address_objects_data
        }
    except Exception as e:
        logger("error", f"Error while getting objects by unique_id: {e}")
        return None
    
def getObjectsByUnique_id_panic(data, unique_id):
    result = [] 
    schedules_data = {}  
    address_objects_data = {}  
    list_rules = data.get('access-rules/ipv4', {}).get('access_rules', [])

    try:
        # Registro de inicio
        logger("start", f"string found in access_rules by unique_id: {unique_id}")

        # Búsqueda de reglas de acceso
        for rule in list_rules:
            try:
                if rule.get('ipv4', {}).get('name', '').startswith("Redpanic_" + str(unique_id)):
                    result.append(rule)
            except KeyError as ke:
                logger("error", f"Error al acceder a clave en access_rules: {ke}")

        # Búsqueda de schedules relacionados
        if 'schedules' in data and 'scheduler' in data['schedules'] and 'schedule' in data['schedules']['scheduler']:
            for rule in result:
                try:
                    rule_schedule_name = rule.get('ipv4', {}).get('schedule', {}).get('name', '')
                    if rule_schedule_name:
                        for schedule in data['schedules']["scheduler"]["schedule"]:
                            if schedule.get('name') == rule_schedule_name:
                                schedules_data[schedule['name']] = schedule
                except KeyError as ke:
                    logger("error", f"Error al acceder a clave en schedules: {ke}")

        # Búsqueda de address-objects filtrados por unique_id en su nombre
        if 'address-objects/ipv4' in data and 'address_objects' in data['address-objects/ipv4']:
            address_objects = data['address-objects/ipv4']["address_objects"]
            for address in address_objects:
                try:
                    address_name = address.get('ipv4', {}).get('name', '')
                    # Filtrar por aquellos que contienen el unique_id en el formato especificado
                    if f"address_object_{unique_id}" in address_name:
                        # Almacenar los address objects filtrados
                        address_objects_data[address_name] = address
                except KeyError as ke:
                    logger("error", f"Error al acceder a clave en address_objects: {ke}")

        # Devolvemos un diccionario con los resultados organizados
        return {
            "access_rules": result,
            "schedules": schedules_data,
            "address_objects": address_objects_data
        }
    except Exception as e:
        logger("error", f"Error while getting objects by unique_id: {e}")
        return None

def saveUserLines(unique_id, data):
    try:
        logger("start", f"Saving user lines for unique_id: {unique_id}")
        
        # Convertir los datos a JSON para almacenar en la base de datos
        rules_json = json.dumps(data["access_rules"])  # data ya contiene las reglas directamente
        queryInsert = "INSERT INTO gatchan_app.user_lines (unique_id, rules) VALUES (%s, %s)"
        db_manager.execute_query(queryInsert, (int(unique_id), rules_json))
        logger("end", f"User lines saved successfully for unique_id: {unique_id}")
        
        # Extraer las UUIDs de las reglas
        uuids = [rule['ipv4']['uuid'] for rule in data["access_rules"]]

        return uuids

    except Exception as e:
        logger("error", f"Error while saving user lines: {e}")
        return None

async def getUserLines(unique_id):
    try:
        query = "SELECT rules FROM gatchan_app.user_lines WHERE unique_id = %s"
        result = db_manager.execute_query(query, (int(unique_id),))

        if not result:
            logger("error", f"User lines not found for unique_id: {unique_id}")
            return {  }

        # Convertir el resultado de la consulta a un objeto JSON
        rules_json = json.loads(result[0][0])

        return rules_json

    except Exception as e:
        logger("error", f"Error while getting user lines: {e}")
        return None
    
async def deleteUserLines(unique_id):
    try:
        query = "DELETE FROM gatchan_app.user_lines WHERE unique_id = %s"
        db_manager.execute_query(query, (int(unique_id),))
        logger("info", f"User lines deleted successfully for unique_id: {unique_id}")
        return True
    except Exception as e:
        logger("error", f"Error while deleting user lines: {e}")
        return False
    
async def addAddressToGroup(addressName, groupName):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()
    uuidGroup = None

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la solicitud para obtener el UUID y los miembros actuales del grupo
    groupInfo = enqueue_firewall_request(url=f"address-groups/ipv4/name/{groupName}", method='GET')
    logger("info", f"Request enqueued to get address group with name {groupName}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {groupInfo} while getting address group")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(groupInfo, None)
        if response:
            if response['status_code'] == 200:
                # Asumiendo que response es el objeto JSON que recibiste del firewall
                # y que solo estás interesado en el primer grupo de direcciones
                logger("response data", response)
                data = response['data']
                current_group = data['address_groups'][0]  # Accede al primer elemento de la lista
                current_addresses = current_group['ipv4']['address_object']['ipv4']  # Ahora sí accedes correctamente
                uuidGroup = current_group['ipv4']['uuid']
                break
            else:
                logger("error", f"Failed to get address group with name {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

    # Agregar la nueva dirección al grupo
    current_addresses.append({"name": addressName})
    logger("PEPE================", f"Current addresses: {current_addresses}")

    # Preparar la actualización del grupo con la nueva lista de direcciones
    update_payload = {
        "address_groups": [
            {
                "ipv4": {
                    "name": groupName,
                    "address_object": {
                        "ipv4": current_addresses
                    }
                }
            }
        ]
    }

    logger("PEPE================", f"Update payload: {update_payload}")

    # Enviar la solicitud para actualizar el grupo con la nueva lista de direcciones
    update_response = enqueue_firewall_request(url=f"address-groups/ipv4/uuid/{uuidGroup}", method='PUT', data=update_payload)
    logger("info", f"Request enqueued to update address group {groupName} with new address {addressName}")

    # Espera y verifica la respuesta de la actualización aquí...
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {update_response} while updating address group")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(update_response, None)
        if response:
            if response['status_code'] == 200:
                # Enviar la solicitud para aplicar los cambios
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                logger("info", f"Request enqueued to apply changes after updating address group {groupName}")
                break
            else:
                logger("error", f"Failed to update address group {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

    # Espera y verifica la respuesta de la solicitud de aplicar cambios aquí...
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while applying changes")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_config_id, None)
        if response:
            if response['status_code'] == 200:
                enqueue_connection_request(logout=True)
                logger("info", f"Changes applied successfully after updating address group {groupName}")
                return True
            else:
                logger("error", f"Failed to apply changes after updating address group {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def removeAddressFromGroup(addressName, groupName):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()
    uuidGroup = None

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la solicitud para obtener el UUID y los miembros actuales del grupo
    groupInfo = enqueue_firewall_request(url=f"address-groups/ipv4/name/{groupName}", method='GET')
    logger("info", f"Request enqueued to get address group with name {groupName}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {groupInfo} while getting address group")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(groupInfo, None)
        if response:
            if response['status_code'] == 200:
                # Asumiendo que response es el objeto JSON que recibiste del firewall
                # y que solo estás interesado en el primer grupo de direcciones
                logger("info", response)
                current_group = response['data']['address_groups'][0]  # Accede al primer elemento de la lista
                current_addresses = current_group['ipv4']['address_object']['ipv4']  # Ahora sí accedes correctamente
                uuidGroup = current_group['ipv4']['uuid']
                break
            else:
                logger("error", f"Failed to get address group with name {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

    # Eliminar la dirección del grupo
    current_addresses = [address for address in current_addresses if address['name'] != addressName]

    # Preparar la actualización del grupo con la nueva lista de direcciones
    update_payload = {
        "address_groups": [
            {
                "ipv4": {
                    "name": groupName,
                    "address_object": {
                        "ipv4": current_addresses
                    }
                }
            }
        ]
    }

    # Enviar la solicitud para actualizar el grupo con la nueva lista de direcciones
    update_response = enqueue_firewall_request(url=f"address-groups/ipv4/uuid/{uuidGroup}", method='PUT', data=update_payload)
    logger("info", f"Request enqueued to update address group {groupName} with new address {addressName}")

    # Espera y verifica la respuesta de la actualización aquí...
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {update_response} while updating address group")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(update_response, None)
        if response:
            if response['status_code'] == 200:
                # Enviar la solicitud para aplicar los cambios
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                logger("info", f"Request enqueued to apply changes after updating address group {groupName}")
                break
            else:
                logger("error", f"Failed to update address group {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

    # Espera y verifica la respuesta de la solicitud de aplicar cambios aquí...
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while applying changes")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_config_id, None)
        if response:
            if response['status_code'] == 200:
                enqueue_connection_request(logout=True)
                logger("info", f"Changes applied successfully after updating address group {groupName}")
                return True
            else:
                logger("error", f"Failed to apply changes after updating address group {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def getAddressesFromGroup(groupName):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la solicitud para obtener el UUID y los miembros actuales del grupo
    groupInfo = enqueue_firewall_request(url=f"address-groups/ipv4/name/{groupName}", method='GET')
    logger("info", f"Request enqueued to get address group with name {groupName}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {groupInfo} while getting address group")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(groupInfo, None)
        if response:
            if response['status_code'] == 200:
                # Asumiendo que response es el objeto JSON que recibiste del firewall
                # y que solo estás interesado en el primer grupo de direcciones
                logger("info", response)
                current_group = response['data']['address_groups'][0]  # Accede al primer elemento de la lista
                current_addresses = current_group['ipv4']['address_object']['ipv4']  # Ahora sí accedes correctamente
                enqueue_connection_request(logout=True)
                address_names = [address['name'] for address in current_addresses]
                return address_names
            else:
                logger("error", f"Failed to get address group with name {groupName}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def createAccessRule(data):
    sendData = {
        "access_rules": data
    }
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la solicitud para crear la regla de acceso
    request_id = enqueue_firewall_request(url='access-rules/ipv4', method='POST', data=sendData)
    logger("info", f"Request enqueued to create access rule")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_id} while creating access rule")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_id, None)
        if response:
            if response['status_code'] == 200:
                # Enviar la solicitud para aplicar los cambios
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                logger("info", f"Request enqueued to apply changes after creating access rule")
                break
            else:
                logger("error", f"Failed to create access rule. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

    # Espera y verifica la respuesta de la solicitud de aplicar cambios aquí...
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while applying changes")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_config_id, None)
        if response:
            if response['status_code'] == 200:
                enqueue_connection_request(logout=True)
                logger("info", f"Changes applied successfully after creating access rule")
                return True
            else:
                logger("error", f"Failed to apply changes after creating access rule. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

def disable_rules_and_return_info(rules_json):
    disabled_rules_info = []  # Lista para almacenar la información de las reglas desactivadas

    # Iterar sobre cada regla de acceso en el JSON
    for rule in rules_json['access_rules']:
        rule_uuid = rule['ipv4']['uuid']  # Obtener el UUID de la regla
        rule_name = rule['ipv4']['name']  # Obtener el nombre de la regla

        # Desactivar la regla modificando el campo 'enable'
        rule['ipv4']['enable'] = False

        json_data = {
            'access_rules': [rule]
        }

        # Agregar la información relevante a la lista de resultados
        disabled_rules_info.append({
            'uuid': rule_uuid,
            'name': rule_name,
            'enable': rule['ipv4']['enable'],
            'rule': json_data
        })

    # Devolver la lista de reglas desactivadas y sus UUIDs
    return disabled_rules_info

def get_user_info(unique_id):
    try:
        # New query to get details from sale_bundle_details and sale_details
        query_new = """
            SELECT
                'sale_bundle_details' AS table_name,
                sbd.id AS detail_id,
                sbd.unique_id,
                sbd.ip,
                '' AS ip2
            FROM
                gatchan.sale_bundle_details sbd
            WHERE
                sbd.unique_id = %s
            UNION ALL
            SELECT
                'sale_details' AS table_name,
                sd.id AS detail_id,
                sd.unique_id,
                sd.ip,
                sd.ip2
            FROM
                gatchan.sale_details sd
            WHERE
                sd.unique_id = %s;
            """
        user_details = db_manager.execute_query(query_new, (unique_id, unique_id))

        logger("info", f"User details: {user_details}")

        # Check if user details are found
        if not user_details:
            logger("error", f"Details for user {unique_id} not found")
            return None

        # Assuming you want to use the first record's IP
        ip = user_details[0][3] if user_details[0][3] else user_details[0][4]

        if not ip:
            logger("error", f"IP for user {unique_id} not found")
            return None
        
        logger("info", f"IP for user {unique_id}: {ip}")

        # Query for product name, replace 'msisdn' with 'unique_id' if necessary
        query_product_name = """
            SELECT COALESCE(i.name) AS product_name
            FROM gatchan.items i
            JOIN gatchan.sale_details sd ON i.id = sd.item_id
            WHERE sd.unique_id = %s
            UNION ALL
            SELECT COALESCE(i.name) AS product_name
            FROM gatchan.items i
            JOIN gatchan.sale_bundle_details sbd ON i.id = sbd.item_id
            WHERE sbd.unique_id = %s
            """
        product_name_result = db_manager.execute_query(query_product_name, (unique_id, unique_id))

        product_name = product_name_result[0][0] if product_name_result else 'Unknown'

        # Clasificación del tipo de producto basada en el nombre
        product_type, product = "LAN", "descarte"  # valores predeterminados
        if "móvil" in product_name.lower():
            product = "movil"
        elif "fibra" in product_name.lower():
            product = "fibra"
        elif "filtrado" in product_name.lower():
            product_type = "VPN"
            product = "filtrado"

        # Combinamos los resultados en un solo objeto
        return {
            "unique_id": unique_id,
            "ip": ip,
            "product_name": product_name,
            "product_type": product_type,
            "product": product
        }
    
    except Exception as e:
        logger("error", f"Error while getting user info: {e}")
        return None

async def delete_address_object(uuid):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la petición de eliminación del horario (schedule)
    request_id = enqueue_firewall_request(url=f"address-objects/ipv4/uuid/{uuid}", method='DELETE')
    logger("info", f"Request enqueued to delete address-objects with UUID {uuid}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_id} while deleting address-objects")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_id, None)
        if response:
            if response['status_code'] == 200:
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera (20 segundos)
                while True:
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    if elapsed_time > timeout:
                        logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while deleting address-objects")
                        enqueue_connection_request(logout=True)
                        return False

                    response = api_responses.pop(request_config_id, None)
                    if response:
                        if response['status_code'] == 200:
                            enqueue_connection_request(logout=True)
                            logger("info", f"address-objects with UUID {uuid} successfully deleted.")
                            return True
                        else:
                            logger("error", f"Failed to delete address-objects with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                            enqueue_connection_request(logout=True)
                            return False

                    await asyncio.sleep(0.5)
            else:
                logger("error", f"Failed to delete address-objects with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def delete_schedule(uuid):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la petición de eliminación del horario (schedule)
    request_id = enqueue_firewall_request(url=f"schedules/uuid/{uuid}", method='DELETE')
    logger("info", f"Request enqueued to delete schedule with UUID {uuid}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_id} while deleting schedule")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_id, None)
        if response:
            if response['status_code'] == 200:
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera (20 segundos)
                while True:
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    if elapsed_time > timeout:
                        logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while deleting schedule")
                        enqueue_connection_request(logout=True)
                        return False

                    response = api_responses.pop(request_config_id, None)
                    if response:
                        if response['status_code'] == 200:
                            enqueue_connection_request(logout=True)
                            logger("info", f"Schedule with UUID {uuid} successfully deleted.")
                            return True
                        else:
                            logger("error", f"Failed to delete schedule with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                            enqueue_connection_request(logout=True)
                            return False

                    await asyncio.sleep(0.5)
            else:
                logger("error", f"Failed to delete schedule with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def delete_access_rule(uuid):
    timeout = 20  # segundos para el tiempo de espera máximo
    start_time = datetime.now()

    # Iniciar la conexión con el firewall
    enqueue_connection_request(login=True)

    # Enviar la petición de eliminación del horario (schedule)
    request_id = enqueue_firewall_request(url=f"access-rules/ipv4/uuid/{uuid}", method='DELETE')
    logger("info", f"Request enqueued to delete access-rules with UUID {uuid}")

    # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout:
            logger("error", f"Timeout waiting for response from the firewall for request {request_id} while deleting access-rules")
            enqueue_connection_request(logout=True)
            return False

        response = api_responses.pop(request_id, None)
        if response:
            if response['status_code'] == 200:
                request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
                # Espera hasta que la respuesta haya sido recibida o hasta que se alcance el tiempo de espera (20 segundos)
                while True:
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    if elapsed_time > timeout:
                        logger("error", f"Timeout waiting for response from the firewall for request {request_config_id} while deleting access-rules")
                        enqueue_connection_request(logout=True)
                        return False

                    response = api_responses.pop(request_config_id, None)
                    if response:
                        if response['status_code'] == 200:
                            enqueue_connection_request(logout=True)
                            logger("info", f"access-rules with UUID {uuid} successfully deleted.")
                            return True
                        else:
                            logger("error", f"Failed to delete access-rules with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                            enqueue_connection_request(logout=True)
                            return False

                    await asyncio.sleep(0.5)
            else:
                logger("error", f"Failed to delete access-rules with UUID {uuid}. Response: {response['status_code']}, {response['data']}")
                enqueue_connection_request(logout=True)
                return False

        await asyncio.sleep(0.5)

async def DeleteDisableDataObjects(data, uuid):
    uuids = {
        'address_object': None,
        'schedule': None,
    }
    names = []

    # Asegúrate de que las claves esperadas existen
    if 'access-rules/ipv4' not in data or 'access_rules' not in data['access-rules/ipv4']:
        logger("error", "Error: 'access-rules/ipv4' data not found")
        return uuids

    for rule in data['access-rules/ipv4']['access_rules']:
        if 'ipv4' in rule and rule['ipv4'].get('uuid') == uuid:
            logger("info", f"Access rule found: {rule}")
            names.append(rule['ipv4']['source']['address']['name'])
            names.append(rule['ipv4']['schedule']['name'])

    # Asegúrate de que los objetos 'address_objects' y 'schedules' están presentes
    if 'address-objects/ipv4' in data and 'address_objects' in data['address-objects/ipv4']:
        for address in data['address-objects/ipv4']['address_objects']:
            if 'ipv4' in address and address['ipv4'].get('name') in names:
                logger("info", f"Address object found: {address}")
                uuids['address_object'] = address['ipv4']['uuid']

    if 'schedules' in data and 'scheduler' in data['schedules'] and 'schedule' in data['schedules']['scheduler']:
        for schedule in data['schedules']['scheduler']['schedule']:
            if schedule.get('name') in names:
                uuid = schedule.get('uuid')  # Usar .get() para acceder de manera segura
                if uuid:  # Verificar si 'uuid' está presente
                    logger("info", f"Schedule found: {schedule}")
                    # egregamos el uuid con la clave 'schedule' al diccionario de uuids para devolverlo
                    uuids['schedule'] = uuid
                else:
                    logger("info", f"Schedule UUID not found for schedule: {schedule}")

    return uuids

async def SaveDisableData(data):
    timeout = timedelta(seconds=20)  # Incremento del tiempo de espera
    start_time = datetime.now()

    logger("saverule", "Request enqueued to login to the firewall")
    logger("data", data)

    # Inicia la conexión con el firewall y envía las solicitudes POST
    enqueue_connection_request(login=True)

    # Almacena los IDs de las solicitudes
    request_ids = {
        'schedules': enqueue_firewall_request(url='schedules', method='POST', data=data['schedule'])
    }

    if "address_object" in data and data["address_object"]:
        request_ids['address-objects/ipv4'] = enqueue_firewall_request(url='address-objects/ipv4', method='POST', data=data['address_object'])

    request_ids['access-rules/ipv4'] = enqueue_firewall_request(url='access-rules/ipv4', method='POST', data=data['access_rule'])

    logger("info", "All requests enqueued for saving objects")

    # Inicializa un diccionario para almacenar las respuestas
    responses = {key: None for key in request_ids.keys()}

    # Espera las respuestas del firewall
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout.total_seconds():
            logger("error", "Timeout waiting for responses from the firewall")
            break

        # Verifica si todas las respuestas han sido recibidas
        all_responses_received = all(request_id in api_responses for request_id in request_ids.values())
        if all_responses_received:
            logger("info", "All responses received from saving objects")
            for key, request_id in request_ids.items():
                responses[key] = api_responses.pop(request_id, None)
            break

        await asyncio.sleep(0.5)

    # Comprobamos que el estado de la respuesta sea 200
    failed_requests = []
    for key, response in responses.items():
        if response is None or response.get('status_code') != 200:
            failed_requests.append(key)
            status_code = response['status_code'] if response is not None else 'None'
            logger("error", f"Request for {key} failed with status code {status_code}")

    if failed_requests:
        error_message = f"Some requests failed: {', '.join(failed_requests)}"
        logger("error", error_message)
        enqueue_connection_request(logout=True)
        return {"error": error_message, "responses": responses}

    # Si todo ha ido bien, envía la solicitud para aplicar cambios
    request_config_id = enqueue_firewall_request(url='config/pending', method='POST', data={})
    start_time = datetime.now()  # Reiniciar el contador de tiempo para la nueva solicitud

    # Espera la respuesta de la solicitud de aplicar cambios
    while True:
        elapsed_time = (datetime.now() - start_time).total_seconds()
        if elapsed_time > timeout.total_seconds():
            logger("error", f"Timeout waiting for response from the firewall for request {request_config_id}")
            enqueue_connection_request(logout=True)
            return {"error": "Timeout waiting for apply changes"}

        if request_config_id in api_responses:
            response = api_responses.pop(request_config_id, None)
            if response and response.get('status_code') == 200:
                logger("info", "Changes applied successfully")
                break
            else:
                logger("error", "Failed to apply changes")
                enqueue_connection_request(logout=True)
                return {"error": "Failed to apply changes"}

        await asyncio.sleep(0.5)

    enqueue_connection_request(logout=True)
    logger("info", "Logout request enqueued after saving objects successfully CREATED")
    return {"success": "All objects saved and changes applied successfully", "responses": responses}

async def get_user_lines(uuid):
    try:
        query = """
            SELECT DISTINCT sale_details.unique_id,'sale_details' AS source_table,sale_details.msisdn
            FROM gatchan.users
            JOIN gatchan.sales ON gatchan.users.uuid = sales.user_id
            JOIN gatchan.sale_details ON sales.id = sale_details.sale_id
            WHERE users.uuid = %s

            UNION

            SELECT
            DISTINCT sale_bundle_details.unique_id,'sale_bundle_details' AS source_table,sale_bundle_details.msisdn
            FROM gatchan.users
            JOIN gatchan.sales ON users.uuid = sales.user_id
            JOIN gatchan.sale_bundles ON sales.id = sale_bundles.sale_id
            JOIN gatchan.sale_bundle_details ON sale_bundles.id = sale_bundle_details.sale_bundle
            WHERE users.uuid = %s;
        """
        result = db_manager.execute_query(query, (uuid, uuid))
        
        if not result:
            logger("error", f"User lines not found for uuid {uuid}")
            return None
        
        _dataFormat = []

        for row in result:
            _dataFormat.append({
                "unique_id": row[0],
                "source_table": row[1],
                "msisdn": row[2]
            })

        logger("info", f"User lines found: {_dataFormat}")

        return _dataFormat
    except Exception as e:
        logger("error", f"Error while getting user lines: {e}")
        return None