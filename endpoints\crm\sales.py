import datetime
from db.connection import db_manager
from core.logs import logger

import json

def get_sales(user_id):
    sales_query = """
        SELECT 
            s.id AS sale_id, 
            s.user_id, 
            s.date,
            sd.id AS sale_detail_id,
            i.id AS item_id,
            i.name AS item_name,
            i.price AS item_price,
            sd.status AS sale_detail_status,
            'Sale' AS type,
            sd.id AS uid,
            sd.deactivation_date AS deactivation,
            sd.msisdn
        FROM 
            gatchan.sales s
        JOIN 
            gatchan.sale_details sd ON s.id = sd.sale_id
        JOIN 
            gatchan.items i ON sd.item_id = i.id
        WHERE 
            s.user_id = %s

        UNION ALL

        SELECT 
            s.id AS sale_id,
            s.user_id, 
            s.date,
            sb.id AS sale_detail_id,
            b.id AS item_id,
            b.name AS bundle_name,
            b.price AS bundle_price,
            sb.`status`,
            'Bundle' AS type,
            sb.id AS uid,
            sb.deactivation_date AS deactivation,
            "N/A" AS msisdn
        FROM 
            gatchan.sales s
        JO<PERSON> 
            gatchan.sale_bundles sb ON s.id = sb.sale_id
        JOIN 
            gatchan.bundle_details bd ON sb.bundle_id = bd.bundle_id
        JOIN 
            gatchan.bundles b ON bd.bundle_id = b.id
        WHERE 
            s.user_id = %s;
        """
    sales_params = [user_id, user_id]

    try:
        print("1")
        sales_result = db_manager.execute_query(sales_query, sales_params)
        print("2")
        print(sales_result)
        # convert sales to json with keys
        final_result = []
        for sale in sales_result:
            sale_dict = {
                "sale_id": sale[0],
                "user_id": sale[1],
                "date": sale[2],
                "sale_detail_id": sale[3],
                "item_id": sale[4],
                "item_name": sale[5],
                "item_price": sale[6],
                "sale_detail_status": sale[7],
                "type": sale[8],
                "uid": sale[9],
                "deactivation": sale[10] if sale[10] else "N/A",
                "msisdn": sale[11] if sale[11] else "N/A",
            }
            final_result.append(sale_dict)

        print(sales_result)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    return final_result


class Sales:
    def __init__(self, sale_id, user_id: str, date: str, items: list):
        self.sale_id = sale_id
        self.user_id = user_id
        self.date = date
        self.items = []


class SaleItem:
    def __init__(
        self,
        sale_detail_id: int,
        id: int,
        name: str,
        price: float,
        type: str,
        bundleSales: list,
        ip: str = None,
        msisdn: str = None,
    ):
        self.sale_detail_id = sale_detail_id
        self.id = id
        self.name = name
        self.price = price
        self.type = type
        self.bundleSales = bundleSales

        self.ip = ip
        self.msisdn = msisdn

    def set_ip(self, ip: str):
        self.ip = ip

    def set_msisdn(self, msisdn: str):
        self.msisdn = msisdn


def get_all_sales():
    class AllSales:
        def __init__(self, sale_id, first_name, date, total_price):
            self.sale_id = sale_id
            self.first_name = first_name
            self.date = date
            self.total_price = total_price

    query = """
    SELECT
        s.id AS sale_id,
        u.name,
        s.date,
        SUM(i.price) AS total_price
    FROM
        gatchan.sales s
    JOIN
        gatchan.users u ON s.user_id = u.uuid
    LEFT JOIN
        gatchan.sale_details sd ON s.id = sd.sale_id
    LEFT JOIN
        gatchan.items i ON sd.item_id = i.id
    GROUP BY
        s.id, u.name, s.date;
    """
    params = ()

    try:
        result = db_manager.execute_query(query, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    if not result:
        logger("error", "Failed to retrieve sales")
        return None

    sales: list = []

    for row in result:
        sale = AllSales(row[0], row[1], row[2], row[3])
        sales.append(sale)

    return sales


def get_sale_by_id(sale_id: int):
    query = "SELECT s.* FROM gatchan.sales s WHERE s.id = %s"
    params = (sale_id,)

    try:
        result = db_manager.execute_query(query, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    if not result:
        logger("error", f"No sale found with id {sale_id}")
        return None

    row = result[0]
    sale = Sales(row[0], row[1], row[2], [])

    query = """
    SELECT
        'Item' AS type,
        sd.id AS sale_detail_id,
        i.id,
        i.name,
        i.price AS price,
        sd.ip,
        sd.msisdn
    FROM
        gatchan.sale_details sd
    JOIN
        gatchan.items i ON sd.item_id = i.id
    WHERE
        sd.sale_id = %s
    UNION ALL
    SELECT
        'Bundle' AS type,
        sb.id AS sale_detail_id,
        b.id AS bundle_id,
        b.name AS bundle_name,
        sb.paid AS price,
        NULL AS ip,
        NULL AS msisdn
    FROM
        gatchan.sale_bundles sb
    JOIN
        gatchan.bundles b ON sb.bundle_id = b.id
    WHERE
        sb.sale_id = %s
    UNION ALL
    SELECT
        'Bundle Item' AS type,
        sbd.id AS sale_detail_id,
        bi.id,
        bi.name,
        bi.price AS price,
        sbd.ip,
        sbd.msisdn
    FROM
        gatchan.sale_bundles sb
    JOIN
        gatchan.sale_bundle_details sbd ON sb.id = sbd.sale_bundle
    JOIN
        gatchan.items bi ON sbd.item_id = bi.id
    WHERE
        sb.sale_id = %s;
    """
    params = (sale.sale_id, sale.sale_id, sale.sale_id)

    try:
        result = db_manager.execute_query(query, params)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

    for row in result:
        item_type, sale_detail_id, item_id, item_name, item_price, ip, msisdn = row
        if item_type == "Item":
            saleItem = SaleItem(
                sale_detail_id,
                item_id,
                item_name,
                item_price,
                item_type,
                [],
                ip,
                msisdn,
            )
            sale.items.append(saleItem)
        elif item_type == "Bundle":
            saleItem = SaleItem(None, item_id, item_name, item_price, item_type, [], ip, msisdn)

            bundle_query = """
                            SELECT
                                'Bundle Item' AS type,
                                sbd.id AS sale_detail_id,
                                bi.id,
                                bi.name,
                                bi.price AS price,
                                sbd.ip,
                                sbd.msisdn
                            FROM
                                gatchan.sale_bundles sb
                            JOIN
                                gatchan.sale_bundle_details sbd ON sb.id = sbd.sale_bundle
                            JOIN
                                gatchan.items bi ON sbd.item_id = bi.id
                            WHERE
                                sb.sale_id = %s
                                AND sb.bundle_id = %s;
                            """
            bundle_params = (sale.sale_id, item_id)

            try:
                bundle_result = db_manager.execute_query(bundle_query, bundle_params)
            except Exception as e:
                logger("error", f"Database query error: {e}")
                return None

            for bundle_row in bundle_result:
                (
                    bundle_type,
                    sale_detail_id,
                    bundle_id,
                    bundle_name,
                    bundle_price,
                    ip,
                    msisdn,
                ) = bundle_row
                bundleItem = SaleItem(
                    sale_detail_id,
                    bundle_id,
                    bundle_name,
                    bundle_price,
                    bundle_type,
                    [],
                    ip,
                    msisdn,
                )
                saleItem.bundleSales.append(bundleItem)

            sale.items.append(saleItem)
        # elif item_type == "Bundle Item":
        #     bundleItem = SaleItem(sale_detail_id, item_id, item_name, item_price, item_type, [], ip, msisdn)
        #     sale.items.append(bundleItem)

    return sale


def insert_sale(user_uuid, items, bundles):
    try:
        # Insert into sales table
        insert_sale_query = "INSERT INTO gatchan.sales (user_id, date) VALUES (%s, %s)"

        print("Inserting sale: ", user_uuid, items, bundles)
        sale_id = db_manager.execute_query(
            insert_sale_query, (user_uuid, datetime.datetime.now()), True
        )

        # Insert into sale_details and sale_bundles tables
        for item_id in items:
            insert_sale_detail_query = (
                "INSERT INTO gatchan.sale_details (sale_id, item_id) VALUES (%s, %s)"
            )
            item_id_col = db_manager.execute_query(
                insert_sale_detail_query, (sale_id, item_id), True
            )

            print("item_id_col", item_id_col)

            # Get the price of the item_id
            get_item_price_query = "SELECT price FROM gatchan.items WHERE id = %s"
            item_price = db_manager.execute_query(get_item_price_query, (item_id,))

            print("item_price", item_price[0][0])

            # Parse from decimal to float
            price_with_tax = float(item_price[0][0]) * 1.21
            print("price_with_tax", price_with_tax)

            # generate_unique_id 
            activation_date = datetime.datetime.now()
            activation_date = activation_date.strftime("%Y-%m-%d %H:%M:%S")

            print("activation_date", activation_date)


            # Update the sale_details table with the price of the item
            # update_sale_detail_query = "UPDATE gatchan.sale_details SET (paid, status, unique_id, activation_date, promotion, promotionStartDate, promotionFinalDate, check_in) = (%s, %s, %s, %s, %s, %s, %s, %s) WHERE id = %s"
            # corrected query
            update_sale_detail_query = "UPDATE gatchan.sale_details SET paid = %s, status = %s, unique_id = %s, activation_date = %s, promotion = %s, promotionStartDate = %s, promoFinalDate = %s, check_in = %s WHERE id = %s"

            print("update_sale_detail_query", update_sale_detail_query)

            # get all unique id from sale_details, sale_bundle_details and sale_bundles
            uniques_query = "SELECT unique_id FROM gatchan.sale_details UNION SELECT unique_id FROM gatchan.sale_bundle_details UNION SELECT unique_id FROM gatchan.sale_bundles"
            unique_ids = db_manager.execute_query(uniques_query)
            print("unique_ids", unique_ids)
            
            # Generate the following unique id (+1 of the biggest unique id) that is not in the database
            u = 0
            for unique_id in unique_ids:
                if type(unique_id[0]) == int:
                    if unique_id[0] > u:
                        u = unique_id[0]
            u += 1

            print("unique_id", u)


            values = (price_with_tax, 0, u, activation_date, "No", "Ninguna", "Ninguna", "Facturable", item_id_col)

            print("values", values)

            tt = db_manager.execute_query(update_sale_detail_query, values, False)

            print("executed", tt)

        for bundle_id in bundles:
            insert_sale_bundle_query = (
                "INSERT INTO gatchan.sale_bundles (sale_id, bundle_id) VALUES (%s, %s)"
            )
            db_manager.execute_query(
                insert_sale_bundle_query, (sale_id, bundle_id), False
            )

        return sale_id
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None


def update_sale_details(sale_id: int, ip, msisdn, type_item: str):
    try:
        # Construct the SQL UPDATE statement
        update_parts = []
        params = []

        if ip is not None:
            update_parts.append("ip = %s")
            params.append(ip)
            print("ANDELE ip", ip)

        if msisdn is not None:
            update_parts.append("msisdn = %s")
            params.append(msisdn)
            print("MARRACO msisdn", msisdn)

        # Only proceed if there are fields to update
        if update_parts:
            if type_item == "Sale":
                update_sql = "UPDATE gatchan.sale_details SET "
                update_sql += ", ".join(update_parts)
                update_sql += " WHERE id = %s"
            elif type_item == "Bundle Item":
                update_sql = "UPDATE gatchan.sale_bundle_details SET "
                update_sql += ", ".join(update_parts)
                update_sql += " WHERE id = %s"
            else:
                return None

            params.append(sale_id)

            print("update_sql", update_sql)
            print("params", params)

            # Execute the update query
            db_manager.execute_query(update_sql, params, False)

    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
def delete_sale_(sale_id: int):
    try:
        FIND_SALE_DETAIL_QUERY = "SELECT id FROM gatchan.sale_details WHERE sale_id = %s"
        FIND_SALE_BUNDLE_QUERY = "SELECT id FROM gatchan.sale_bundles WHERE sale_id = %s"

        sale_detail_ids = db_manager.execute_query(FIND_SALE_DETAIL_QUERY, (sale_id,))
        sale_bundle_ids = db_manager.execute_query(FIND_SALE_BUNDLE_QUERY, (sale_id,))

        DELETE_SALE_DETAIL_QUERY = "DELETE FROM gatchan.sale_details WHERE id = %s"
        DELETE_SALE_BUNDLE_QUERY = "DELETE FROM gatchan.sale_bundles WHERE id = %s"
        DELETE_SALE_BUNDLE_DETAILS_QUERY = "DELETE FROM gatchan.sale_bundle_details WHERE sale_bundle = %s"

        for sale_detail_id in sale_detail_ids:
            db_manager.execute_query(DELETE_SALE_DETAIL_QUERY, (sale_detail_id[0],), False)

        for sale_bundle_id in sale_bundle_ids:
            db_manager.execute_query(DELETE_SALE_BUNDLE_DETAILS_QUERY, (sale_bundle_id[0],), False)

        for sale_bundle_id in sale_bundle_ids:
            db_manager.execute_query(DELETE_SALE_BUNDLE_QUERY, (sale_bundle_id[0],), False)
            
        DELETE_SALE_QUERY = "DELETE FROM gatchan.sales WHERE id = %s"
        db_manager.execute_query(DELETE_SALE_QUERY, (sale_id,), False)

    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
def get_sale_state_(sale_id: int):
    try:
        CREATED_SALED_QUERY = "SELECT date FROM gatchan.sales WHERE id = %s"
        ALL_SALE_STATES = "SELECT * FROM gatchan.sale_state WHERE sale_id = %s"

        sale_date = db_manager.execute_query(CREATED_SALED_QUERY, (sale_id,))
        sale_states = db_manager.execute_query(ALL_SALE_STATES, (sale_id,))

        # Create a list of sales states
        sales = []
        for state in sale_states:
            sales.append({
                'id': state[0],
                'state': state[2],
                'date': state[3]
            })

        sales.append({
            'id': 0,
            'state': "CREATED",
            'date': sale_date[0][0]
        })

        # Return a dictionary with the sale_id and the sales states
        return {
            'sale_id': sale_id,
            'sales': sales
        }
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
def update_sale_state_(sale_id: int, state: str):
    try:
        current_date = datetime.datetime.now()
        INSERT_SALE_STATE_QUERY = "INSERT INTO gatchan.sale_state (sale_id, state, date) VALUES (%s, %s, %s)"
        db_manager.execute_query(INSERT_SALE_STATE_QUERY, (sale_id, state, current_date), False)
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
def get_sale_notes(id: str):
    NOTES_QUERY = "SELECT notes FROM gatchan.sales WHERE id = %s"
    params = (id,)

    # Console log the query with the params
    print(NOTES_QUERY % params)
    logger("info", f"Query: {NOTES_QUERY % params}")

    try:
        result = db_manager.execute_query(NOTES_QUERY, params)
        logger("info", f"Result: {result}")
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    
    if not result:
        logger("error", "Failed to retrieve customer notes")
        return None
    
    logger("info", f"Notes: {result[0][0]}")
    return result[0][0]

def update_sale_notes_(id: str, notes: str):
    UPDATE_NOTES_QUERY = "UPDATE gatchan.sales SET notes = %s WHERE id = %s"
    
    # Check if notes is empty, if so replace it with an empty array
    notes = notes if notes else []
    
    data_to_save = {"notes": notes}
    notes_json = json.dumps(data_to_save)
    
    params = (notes_json, id)

    try:
        db_manager.execute_query(UPDATE_NOTES_QUERY, params)
        return True
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None