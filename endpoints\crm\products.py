from db.connection import db_manager
from core.logs import logger

from endpoints.models.crm.Product import Product

def generate_product_id():
    query = "SELECT product_id FROM gatchan.items ORDER BY product_id DESC LIMIT 1"
    result = db_manager.execute_query(query)

    base_id = "ABS-"  # Asegúrate de que esto coincida con el prefijo en la base de datos

    # Inicialización predeterminada
    max_middle_num, max_last_num = 0, 0

    # Obtener y descomponer el último ID
    if result and result[0] and result[0][0]:
        latest_id = result[0][0]
        parts = latest_id.split('-')
        if len(parts) == 3 and parts[0] == base_id.rstrip('-'):  # Remueve el guión para la comparación
            max_middle_num, max_last_num = int(parts[1]), int(parts[2])

    # Incrementar los números
    max_last_num += 1
    if max_last_num >= 100:
        max_middle_num += 1
        max_last_num = 0

    # Formatear el nuevo ID
    new_id = f"{base_id}{str(max_middle_num).zfill(3)}-{str(max_last_num).zfill(2)}"
    
    return new_id

def get_products():
    queryProducts = "SELECT id,name,price,provider,product_id,`status` FROM gatchan.items ORDER BY product_id DESC"
    queryBundles = "SELECT b.id, b.name, b.price as total_price FROM gatchan.bundles b JOIN gatchan.bundle_details bd ON b.id = bd.bundle_id JOIN gatchan.items i ON bd.item_id = i.id GROUP BY b.id, b.name;"

    try:
        resultProducts = db_manager.execute_query(queryProducts)
        resultBundles = db_manager.execute_query(queryBundles)

        if not resultProducts or not resultBundles:
            logger("error", "Failed to retrieve products")
            return None
        
        products = []
        for row in resultProducts:
            products.append({
                "id": row[0],
                "name": row[1],
                "price": row[2],
                "provider": row[3],
                "product_id": row[4],
                "status": row[5] 
            })

        bundles = []
        for row in resultBundles:
            bundles.append({
                "id": row[0],
                "name": row[1],
                "total_price": row[2]
            })

        return {
            "products": products,
            "bundles": bundles
        }
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

def get_product(id: int):
    # Query para obtener detalles del producto
    query_product = """
    SELECT 
        id, name, description, price,
        is_recurring, recurrence_period, speed, data_bonus,
        sms_count, voice_calls, product_type, line_type,
        provider, status, is_filtered
    FROM 
        gatchan.items
    WHERE 
        id = %s
    """

    # Query para obtener los usuarios asociados con el producto
    query_users = """
    SELECT 
        u.uuid,
        u.name,
        u.email
    FROM 
        gatchan.items i
    JOIN 
        gatchan.sale_details sd ON i.id = sd.item_id
    JOIN 
        gatchan.sales s ON sd.sale_id = s.id
    JOIN 
        gatchan.users u ON s.user_id = u.uuid
    WHERE 
        i.id = %s
    """

    try:
        # Ejecutando la consulta del producto
        product_result = db_manager.execute_query(query_product, (id,))
        if not product_result:
            logger("error", "Failed to retrieve product")
            return None

        # Construyendo el diccionario del producto
        product = {
            "id": product_result[0][0],
            "name": product_result[0][1],
            "description": product_result[0][2],
            "price": product_result[0][3],
            "recurring": product_result[0][4] == 1,
            "recurrence_period": product_result[0][5],
            "speed": product_result[0][6],
            "data_bonus": product_result[0][7],
            "sms_count": product_result[0][8],
            "voice_calls": product_result[0][9],
            "product_type": product_result[0][10],
            "line_type": product_result[0][11],
            "provider": product_result[0][12],
            "status": product_result[0][13] == 1,
            "is_filter": product_result[0][14] == 1
        }

        # Ejecutando la consulta de los usuarios
        users_result = db_manager.execute_query(query_users, (id,))
        users = [{"uuid": u[0], "name": u[1], "email": u[2]} for u in users_result]

        # Añadiendo los usuarios al producto
        product["users"] = users

        return {
            "product": product,
            "users": users
        }

    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None

def create_product_(product: Product):
    new_product_id = generate_product_id()

    # print("Generated product ID:", new_product_id)

    query_product = """
    INSERT INTO gatchan.items (
        product_id, name, description, price,
        is_recurring, recurrence_period, speed, data_bonus,
        sms_count, voice_calls, product_type, line_type,
        provider, status, is_filtered
    ) VALUES ( %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                %s, %s, %s, %s, %s, %s)
    """

    if not new_product_id:
        print("No new product ID")
        logger("error", "Failed to generate a new product ID")
        return None
    
    try:
        params = (
            new_product_id, product.name, product.description, product.price,
            product.is_recurring, product.recurrence_period, product.speed, product.data_bonus,
            product.sms_count, product.voice_calls, product.product_type, product.line_type,
            product.provider, product.status, product.is_filtered
        )

        # print("Executing query...")
        result = db_manager.execute_query(query_product, params, True)
        # print("Query executed")

        if not result:
            print("No result")
            logger("error", "Failed to create product")
            return None
        
        # Devuelve un diccionario con la información del producto creado
        created_product = {
            "product_id": new_product_id,
            "name": product.name,
            "description": product.description,
            "price": product.price,
            "recurring": product.is_recurring,
            "recurrence_period": product.recurrence_period,
            "speed": product.speed,
            "data_bonus": product.data_bonus,
            "sms_count": product.sms_count,
            "voice_calls": product.voice_calls,
            "product_type": product.product_type,
            "line_type": product.line_type,
            "provider": product.provider,
            "status": product.status,
            "is_filtered": product.is_filtered
        }

        # print("Created product:", created_product)
        
        return created_product
    
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None
    

def update_product_(product: dict):
    query_product = """
    UPDATE gatchan.items SET
        name = %s,
        description = %s,
        price = %s,
        is_recurring = %s,
        recurrence_period = %s,
        speed = %s,
        data_bonus = %s,
        sms_count = %s,
        voice_calls = %s,
        product_type = %s,
        line_type = %s,
        provider = %s,
        status = %s,
        is_filtered = %s
    WHERE
        id = %s
    """

    try:
        params = (
            product.get("name"),
            product.get("description"),
            product.get("price"),
            product.get("recurring"),
            product.get("recurrence_period"),
            product.get("speed"),
            product.get("data_bonus"),
            product.get("sms_count"),
            product.get("voice_calls"),
            product.get("product_type"),
            product.get("line_type"),
            product.get("provider"),
            product.get("status"),
            product.get("is_filtered"),
            product.get("id")
        )

        result = db_manager.execute_query(query_product, params, True)

        if not result:
            logger("error", "Failed to update product")
            return None
        
        return True
    
    except Exception as e:
        logger("error", f"Database query error: {e}")
        return None