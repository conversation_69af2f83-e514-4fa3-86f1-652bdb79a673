from fastapi import HTTPException, Depends, Request, Response, File, Form, UploadFile
from pydantic import BaseModel
from typing import Optional, List
from core.utils import requires_auth
from core.logs import logger
from endpoints.auth.login import decode_token
from endpoints.crm.emails import get_email_, get_emails_, main_email, reply_to_email
from endpoints.crm.invoice import genhtml, portabilidadHtml
from main import app
import pdfkit
import json
from db.connection import db_manager

from endpoints.crm.customers import *
from endpoints.crm.products import *
from endpoints.crm.sales import *
from endpoints.crm.orders import *

from endpoints.models.crm.Customer import Customer
from endpoints.models.crm.Product import Product

@app.get("/crm/customers")
async def read_customers():
    customers = get_customers()
    if not customers:
        raise HTTPException(status_code=500, detail="Internal server error")
    return customers

@app.post("/crm/customer")
async def create_customer(request: Request):
    form_data = await request.form()
    uuid = form_data.get("uuid")

    print("form_data", form_data)

    if not uuid:
        print("No uuid")
        raise HTTPException(status_code=400, detail="Incorrect uuid")
    
    customer = get_customer(uuid)
    if not customer:
        print("No customer")
        raise HTTPException(status_code=400, detail="Incorrect uuid")
    
    return customer

@app.get("/crm/customer/notes/{uuid}")
async def read_customer_notes(uuid: str):
    customer = get_customer_notes(uuid)
    if not customer:
        raise HTTPException(status_code=500, detail="Internal server error")

    print("customer", customer)
    return customer


@app.post("/crm/customer/notes/{uuid}")
async def update_customer_notes(uuid: str, request: Request):
    json_data = await request.json()

    if not uuid:
        raise HTTPException(status_code=400, detail="Incorrect uuid")

    notes = json_data.get("notes")
    notes = notes if notes else []

    update_customer_notes_(uuid, notes)

    print(json_data)
    return {"status": "success"}


@app.get("/crm/customer/permanence/{uuid}")
async def read_customer_permanence(uuid: str):
    customer = get_customer_permanence(uuid)
    if not customer:
        raise HTTPException(status_code=500, detail="Internal server error")
    return customer

@app.get("/crm/products")
async def read_products():
    products = get_products()
    if not products:
        raise HTTPException(status_code=500, detail="Internal server error")
    return products

@app.post("/crm/product")
async def get_all_products(request: Request):
    form_data = await request.form()

    # Try to get the 'product_id' from the form data.
    product_id = form_data.get("product_id")
    
    # Check if 'product_id' is present and is a digit.
    if product_id is None or not product_id.isdigit():
        raise HTTPException(status_code=400, detail="Incorrect or missing product_id.")

    # Convert 'product_id' to an integer.
    id = int(product_id)

    # Now use 'id' to get the product information.
    product = get_product(id)

    if not product:
        raise HTTPException(status_code=404, detail="Product not found.")

    return product

@app.post("/crm/product/create", response_model=Product)
async def create_product_endpoint(product: Product):
    product_data = create_product_(product)

    if not product_data:
        raise HTTPException(status_code=500, detail="Internal server error")

    product.product_id = product_data.get("product_id")

    return product

@app.get("/crm/sales/user/{uuid}")
async def read_sales(uuid: str):
    sales = get_sales(uuid)
    if not sales:
        raise HTTPException(status_code=500, detail="Internal server error")
    return sales

@app.get("/crm/sales")
async def read_sales():
    sales = get_all_sales()
    if not sales:
        raise HTTPException(status_code=500, detail="Internal server error")
    return sales

@app.get("/crm/sale/{sale_id}")
async def read_sale(sale_id: int):
    sale = get_sale_by_id(sale_id)
    if not sale:
        raise HTTPException(status_code=500, detail="Internal server error")
    return sale

@app.get("/crm/orders")
async def read_orders():
    orders = get_orders()

    if not orders:
        raise HTTPException(status_code=500, detail="Internal server error")
    
    return orders

# Create
@app.post("/crm/customer/create")
async def create_customer(customer: Customer):
    """
    Receives customer data as JSON and logs each field.
    
    Args:
    customer (Customer): The customer data as a Pydantic model.
    
    Returns:
    dict: A confirmation message.
    """
    customer_data = customer.dict()

    customer = create_customer_(customer_data)
    
    if not customer:
        raise HTTPException(status_code=500, detail="Internal server error")
    
    return {"message": "Customer created successfully", "customer": customer}


class Sale(BaseModel):
    uuid: str
    items: List[str]
    bundles: List[str]

@app.post("/crm/sale/create")
async def create_sale(sale: Sale) -> str:
    sale_data = sale.dict()
    saleinsert = insert_sale(sale_data.get("uuid"), sale_data.get("items"), sale_data.get("bundles"))
    
    if not saleinsert:
        raise HTTPException(status_code=500, detail="Internal server error")
    
    return "Sale created successfully"


class SaleItemUpdate(BaseModel):
    sale_detail_id: int
    ip: str = None
    msisdn: str = None
    type_item: str = None


class ItemActivationUpdate(BaseModel):
    uid: int
    activation_date: str
    type: str


class ItemStatusUpdate(BaseModel):
    uid: int
    status: int
    deactivation_date: Optional[str] = None
    type: str

@app.put("/update-sale/{sale_id}")
async def update_sale(sale_item_update: SaleItemUpdate):
    # Print the sale id, ip and msisdn in different lines.
    print(f"sale_id: {sale_item_update.sale_detail_id}")
    print(f"ip: {sale_item_update.ip}")
    print(f"msisdn: {sale_item_update.msisdn}")
    print(f"type_item: {sale_item_update.type_item}")

    # Update the sale details in the database.
    update_sale_details(sale_item_update.sale_detail_id, sale_item_update.ip, sale_item_update.msisdn, sale_item_update.type_item)

    return {"status": "success"}


@app.put("/update-product/{product_id}")
async def update_product(data: dict):
    update_product_(data)

    return {"status": "success"}

@app.put("/update-customer/{uuid}")
async def update_customer(data: dict):
    update_customer_(data)

    return {"status": "success"}


@app.post("/crm/item/update-activation")
async def update_item_activation_date_endpoint(item_update: ItemActivationUpdate):
    """
    Update the activation date for an item (Sale or Bundle Item)

    Request body:
    {
        "uid": "item_uid",
        "activation_date": "2024-01-15",
        "type": "item_type"
    }
    """
    try:
        success = update_item_activation_date(
            uid=item_update.uid,
            activation_date=item_update.activation_date,
            item_type=item_update.type
        )

        if success:
            return {"status": "success", "message": "Activation date updated successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to update activation date")

    except Exception as e:
        logger("error", f"Error in update_item_activation_date_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/crm/item/update-status")
async def update_item_status_endpoint(item_update: ItemStatusUpdate):
    """
    Update the status of an item (Sale or Bundle Item)

    Request body:
    {
        "uid": "item_uid",
        "status": 1, // 1 for active, 0 for inactive
        "deactivation_date": "2024-01-15", // only when changing to inactive
        "type": "item_type"
    }
    """
    try:
        # Validate status value
        if item_update.status not in [0, 1]:
            raise HTTPException(status_code=400, detail="Status must be 0 (inactive) or 1 (active)")

        # If setting to inactive, deactivation_date should be provided
        deactivation_date = item_update.deactivation_date if item_update.status == 0 else None

        success = update_item_status(
            uid=item_update.uid,
            status=item_update.status,
            deactivation_date=deactivation_date,
            item_type=item_update.type
        )

        if success:
            status_text = "active" if item_update.status == 1 else "inactive"
            return {"status": "success", "message": f"Item status updated to {status_text}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to update item status")

    except HTTPException:
        raise
    except Exception as e:
        logger("error", f"Error in update_item_status_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/crm/generate-sale/{sale_id}")
async def generate_invoice(sale_id: str):
    html_content = genhtml(sale_id)

    try:
        # pdf = pdfkit.from_url('http://google.com', False)
        pdf = pdfkit.from_string(html_content, False)
        return Response(content=pdf, media_type="application/pdf")
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.exception(f"PDF generation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
    

@app.get("/crm/generate-sale/{sale_id}/{number}")
async def generate_invoice(sale_id: str, number: str):
    html_content = portabilidadHtml(sale_id, number)

    try:
        # pdf = pdfkit.from_url('http://google.com', False)
        pdf = pdfkit.from_string(html_content, False)
        return Response(content=pdf, media_type="application/pdf")
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.exception(f"PDF generation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@app.post("/crm/list-sales-details")
async def list_sales_details():
    try:
        details = get_sales_and_bundle_details()
        return details
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/crm/list-sales-details/edit")
async def list_sales_details_edit(sale_item_update: SaleItemUpdate):
    try:
        update_sale_details(sale_item_update.sale_detail_id, sale_item_update.ip, sale_item_update.msisdn, sale_item_update.type_item)
        
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/crm/send-emails")
async def get_send_emails():
    try:
        r = get_emails()
        return r
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/crm/send-emails")
async def post_send_emails(
    subject: str = Form(...),
    body: str = Form(...),
    attachments: List[UploadFile] = File([]),
    destinations: str = Form(...)
):
    try:
        # Deserializa destinations de JSON a un objeto Python
        destinations_list = json.loads(destinations)
        status_code, response = send_emails(subject, body, destinations_list, attachments)
        
        return {"status_code": status_code, "response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@app.get("/crm/get-emails/{index}")
async def get_emails(index):
    try:
        r = get_emails_(index)
        return r
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/crm/get-email/{email_id}")
async def get_email(email_id: int):
    try:
        r = get_email_(email_id)
        return r
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/crm/user/emails/{uuid}")
async def get_user_emails(uuid: str):
    try:
        GET_EMAIL_QUERY = "SELECT email FROM gatchan.users WHERE uuid = %s"
        email = db_manager.execute_query(GET_EMAIL_QUERY, (uuid,))
        logger("INFO", f"Email for user {uuid}: {email[0][0]}")
        if email[0][0] is None:
            raise HTTPException(status_code=404, detail="User not found")
        r = main_email(email[0][0])
        logger("INFO", f"Emails for user {uuid}: {r}")
        return r
    except Exception as e:
        logger("ERROR", f"Error getting emails for user {uuid}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/crm/user/delete/{uuid}")
async def delete_user(uuid: str):
    try:
        logger("INFO", f"Deleting user {uuid}")

        # Update all the sales user_id to "test"
        UPDATE_SALES_QUERY = "UPDATE gatchan.sales SET user_id = '75740245-4ffa-41c4-ae83-7d9d47f7fd9d' WHERE user_id = %s"
        db_manager.execute_query(UPDATE_SALES_QUERY, (uuid,))

        ID_QUERY = "SELECT billing_id, shipping_id FROM gatchan.users WHERE uuid = %s"
        billing_id, shipping_id = db_manager.execute_query(ID_QUERY, (uuid,))[0]

        DELETE_BILLING_QUERY = "DELETE FROM gatchan.billing WHERE id = %s"
        DELETE_SHIPPING_QUERY = "DELETE FROM gatchan.shipping WHERE id = %s"
        DELETE_USER_QUERY = "DELETE FROM gatchan.users WHERE uuid = %s"
        
        try:
            db_manager.execute_query(DELETE_BILLING_QUERY, (billing_id,))
            db_manager.execute_query(DELETE_SHIPPING_QUERY, (shipping_id,))
            db_manager.execute_query(DELETE_USER_QUERY, (uuid,))
        except:
            HTTPException(status_code=500, detail="Error deleting user")
            raise
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.delete("/crm/sale/delete/{sale_id}")
async def delete_sale(sale_id: int):
    try:
        delete_sale_(sale_id)
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/crm/sale/state/{sale_id}")
async def get_sale_state(sale_id: int):
    try:
        r = get_sale_state_(sale_id)
        return r
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/crm/sale/state/{sale_id}")
async def update_sale_state(request: Request, sale_id: int):
    try:
        json_data = await request.json()
        state = json_data.get("status")
        update_sale_state_(sale_id, state)
        return {"status": f"Sale {sale_id} updated to {state}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/crm/email/reply")
async def reply_email(request: Request):
    try:
        json_data = await request.json()
        email_address = json_data.get("email_address")
        thread_id = json_data.get("thread_id")
        body = json_data.get("body")

        # return {"email_address": email_address, "thread_id": thread_id, "body": body}

        rep = reply_to_email(email_address, thread_id, body)
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@app.post("/crm/user/dissable/{uuid}")
async def dissable_user(uuid: str):
    try:
        print("Dissabling user", uuid)
        # DISSABLE_USER_QUERY = "UPDATE gatchan.users SET status = 'dissabled' WHERE uuid = %s"
        # db_manager.execute_query(DISSABLE_USER_QUERY, (uuid,))
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/crm/item/dissable")
async def dissable_item(request: Request):
    try:
        json_data = await request.json()
        print(json_data)

        # {'uid': 3342, 'date': '2024-05-08', 'type': 'Sale'}
        newDate = json_data.get("date")
        newDate = newDate + " 00:00:00"
        
        try:
            if json_data.get("type") == "Sale":
                DISSABLE_SALE_QUERY = "UPDATE gatchan.sale_details SET STATUS = 0, deactivation_date = %s WHERE id = %s"
                db_manager.execute_query(DISSABLE_SALE_QUERY, (newDate, json_data.get("uid")))
            # elif json_data.get("type") == "Bundle":
                # DISSABLE_BUNDLE_QUERY = "UPDATE gatchan.bundles SET status = '0' WHERE id = %s"
                # db_manager.execute_query(DISSABLE_BUNDLE_QUERY, (json_data.get("uid"),))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/crm/sale/notes/{sale_id}")
async def read_sale_notes(sale_id: int):
    try:
        sale = get_sale_notes(sale_id)
        print("sale", sale)
        # convert sale(string) to json
        sale = json.loads(sale)
        return sale
    except Exception as e:
        print("Error", e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/crm/sale/notes/update")
async def update_sale_notes(request: Request):
    try:
        json_data = await request.json()
        sale_id = json_data.get("saleId")

        if not sale_id:
            raise HTTPException(status_code=400, detail="Incorrect uuid")

        notes = json_data.get("notes")
        notes = notes if notes else []

        print("notes", notes)

        update_sale_notes_(sale_id, notes)

        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/crm/users/baja")
async def baja_user(request: Request):
    try:
        json_data = await request.json()
        user_id = json_data.get("user_id")
        result = baja_user_(user_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))