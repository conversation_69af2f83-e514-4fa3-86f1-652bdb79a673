from db.connection import db_manager
from core.logs import logger
import json

def get_user_by_uuid(uuid: str):
    # Get user information based on the token
    query = "SELECT name, mobile_phone, email, address, postal_code, city, province, national_id FROM gatchan.users WHERE uuid = %s"
    params = (uuid,)
    logger("info", f"Getting user information with uuid {uuid}")
    print("Getting user information with uuid {uuid}")
    try:
        user = db_manager.execute_query(query, params)
        print(user)

        # Convert the user data to a dictionary
        user_dict = {
            "name": user[0][0],
            "mobile_phone": user[0][1],
            "email": user[0][2],
            "address": user[0][3],
            "postal_code": user[0][4],
            "city": user[0][5],
            "province": user[0][6],
            "national_id": user[0][7],
        }

        return user_dict
    except Exception as e:
        logger("error", f"Failed to get user information: {e}")
        print(f"Failed to get user information: {e}")
        return None
    
def get_user_contact_id(uuid: str):
    query = "SELECT contact_id FROM gatchan.users WHERE uuid = %s"

    params = (uuid,)
    logger("info", f"Getting user contact id with uuid {uuid}")

    try:
        user = db_manager.execute_query(query, params)

        return user[0][0]
    except Exception as e:
        logger("error", f"Failed to get user contact id: {e}")
        print(f"Failed to get user contact id: {e}")
        return None