from db.connection import db_manager
from core.logs import logger

def get_sales_and_bundle_details():
    # Updated query for sale_details to include item name
    query_sales = """
        SELECT 
            sd.id, sd.sale_id, sd.item_id, sd.ip, sd.msisdn, u.name, u.contact_id, i.name AS item_name, u.uuid
        FROM 
            gatchan.sale_details AS sd
            INNER JOIN gatchan.sales AS s ON sd.sale_id = s.id
            INNER JOIN gatchan.users AS u ON s.user_id = u.uuid
            INNER JOIN gatchan.items AS i ON sd.item_id = i.id
        WHERE 
            (sd.ip IS NULL OR sd.ip = '') AND (sd.msisdn IS NULL OR sd.msisdn = '')
    """
    result_sales = db_manager.execute_query(query_sales)

    # Updated query for bundle_details to include item name and JOINs
    query_bundles = """
        SELECT 
        sbd.id, 
        sbd.sale_bundle, 
        sbd.item_id, 
        MAX(sbd.ip) AS ip, 
        MAX(sbd.msisdn) AS msisdn, 
        MAX(u.name) AS name, 
        MAX(u.contact_id) AS contact_id,
        i.name AS item_name, -- Nombre del item (paquete)
        u.uuid
        FROM 
        gatchan.sale_bundle_details AS sbd
        INNER JOIN gatchan.items AS i ON sbd.item_id = i.id -- Unión con la tabla de items para obtener el nombre
        INNER JOIN gatchan.sale_bundles AS sb ON sbd.sale_bundle = sb.id 
        INNER JOIN gatchan.sales AS s ON sb.sale_id = s.id 
        INNER JOIN gatchan.users AS u ON s.user_id = u.uuid 
        WHERE 
        (sbd.ip IS NULL OR sbd.ip = '') AND 
        (sbd.msisdn IS NULL OR sbd.msisdn = '') 
        GROUP BY 
        sbd.id, 
        i.name; -- Agrupar también por el nombre del item
    """
    result_bundles = db_manager.execute_query(query_bundles)

    logger("INFO", "Sales and bundle details retrieved successfully")

    sales_details = []

    # Add the results of sale_details to the corresponding list, now including item_name
    for row in result_sales:
        sales_details.append({
            "id": row[0],
            "sale_id": row[1],
            "item_id": row[2],
            "ip": row[3],
            "msisdn": row[4],
            "user_name": row[5],
            "contact_id": row[6],
            "item_name": row[7],  # Include item_name in the dictionary
            "user_uuid": row[8],
            "type_item": "Sale"
        })

    # Add the results of bundle_details to the corresponding list, now including item_name
    for row in result_bundles:
        sales_details.append({
            "id": row[0],
            "sale_id": row[1],
            "item_id": row[2],
            "ip": row[3],
            "msisdn": row[4],
            "user_name": row[5],
            "contact_id": row[6],
            "item_name": row[7],  # Include item_name in the dictionary
            "user_uuid": row[8],
            "type_item": "Bundle Item"
        })

    # Return a dictionary with both lists
    return sales_details

def get_orders():
    pass