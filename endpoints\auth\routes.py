from fastapi import HTT<PERSON><PERSON>x<PERSON>, Depends, Request
from fastapi.security import OAuth2PasswordRequestForm 
from endpoints.models.auth.token import Token
from endpoints.models.auth.user import User
from endpoints.auth.login import *
from endpoints.auth.register import *
from main import app
from dotenv import load_dotenv
import os
from core.utils import requires_auth, send_email
from core.logs import logger

load_dotenv()

@app.post("/register", response_model=User)
async def register_user(user: User):
    if check_user_exists(user.email):
        raise HTTPException(status_code=400, detail="Email already exists")
    
    if not register(user):
        raise HTTPException(status_code=400, detail="Something went wrong")
    
    return user

@app.post("/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = get_user(form_data.username)
    
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect username")
    if not verify_password(form_data.password, user[3]):
        raise HTTPException(status_code=400, detail="Incorrect password")

    access_token = create_access_token(data={
        "UUID": user[1],
        "username": user[2],
    })

    if not save_token(access_token, user[1]):
        raise HTTPException(status_code=500, detail="Server internal error")

    return {
        "access_token": access_token,
        "token_type": "bearer",
    }

@app.post("/forgotpassword/create")
async def forgot_password(request: Request):
    form_data = await request.form()
    email = form_data.get("email")
    user = get_user_by_email(email)

    if not user:
        raise HTTPException(status_code=400, detail="Incorrect email")

    token_forgot = create_access_token(data={
        "UUID": user[0],
        "type": "reset_password",
        "email": user[1],
    })

    token_info = save_token(token_forgot, user[0], True)

    if token_info['action'] == 'error':
        raise HTTPException(status_code=500, detail="Server internal error")
    
    send_email(user[1], "Reset password", "Reset password link: cliente.gatchan.es/password-reset?token=" + token_info['token'])
    
    return {
        "status": "success",
        "message": "Email sent",
        "token_info": token_info,
    }

@app.post("/forgotpassword/reset")
async def reset_password(request: Request):
    form_data = await request.form()
    token = form_data.get("token")
    password = form_data.get("password")
    decode = decode_token(token)

    if decode["type"] != "reset_password":
        raise HTTPException(status_code=400, detail="Incorrect token")

    if not update_password(decode["UUID"], password):
        raise HTTPException(status_code=500, detail="Server internal error")
    
    if not delete_token(token):
        raise HTTPException(status_code=500, detail="Server internal error")
    
    send_email(decode["email"], "Password updated", "Your password has been updated")

    return {
        "status": "success",
        "message": "Password updated",
    }

@app.post("/checktoken")
async def check_token_url(token: str = Depends(oauth2_scheme)):
    return check_token(token)

@app.get("/users/me")
async def read_users_me_url(token: str = Depends(oauth2_scheme)):
    return decode_token(token)

@app.post("/crm/register")
async def register_user_crm(request: Request):
    form_data = await request.form()

    username = form_data.get("username")
    password = form_data.get("password")

    if not registerCRM(username, password):
        raise HTTPException(status_code=500, detail="Server internal error")
    
    return {
        "status": "success",
        "message": "User created",
    }

@app.post("/crm/login", response_model=Token)
async def login_for_access_token_crm(form_data: OAuth2PasswordRequestForm = Depends()):
    user = get_user_crm(form_data.username)
    
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect username")
    if not verify_password(form_data.password, user[3]):
        raise HTTPException(status_code=400, detail="Incorrect password")

    access_token = create_access_token(data={
        "UUID": user[1],
        "username": user[2],
    })

    return {
        "access_token": access_token,
        "token_type": "bearer",
    }