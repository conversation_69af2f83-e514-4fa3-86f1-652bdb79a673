import openai
from core.openai.user import User 
import re

def chat_with_gpt(new_question, user_id: str, name: str):
    # Create an object to store the user's chat history
    person_historic = User(user_id, "Eres un asistente virtual")

    # Add the user's message (new_question) to the chat history
    person_historic.add_chat({"role": "user", "content": new_question})

    # Call GPT-3 to respond to the user's message
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=person_historic.get_chat_history(),
    )
    response.choices[0].message['content'] = re.sub(r"^Respuesta:\s", "", response.choices[0].message['content'])

    # Add the GPT-3 response to the user's chat history
    person_historic.add_chat({"role": "system", "content": response.choices[0].message['content']})

    # Return the GPT-3 response
    return response.choices[0].message['content']

