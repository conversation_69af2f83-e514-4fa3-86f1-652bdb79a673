import json
import os
from db.connection import db_manager


folder_path = os.path.dirname(os.path.abspath(__file__))
folder_path = folder_path.replace("endpoints/statistics", "statistics")
catalog_name = "/date/date_catalog.json"
products_name = "/date/date_product.json"

def get_items(file_path):
    file_content = open(file_path, "r").read()
    file_content = json.loads(file_content)
    return file_content["statistics"]["items"]

def get_item_product_ids(items):
    item_product_ids = []
    for item in items:
        item_product_ids.append(item["item_product_id"])
    return item_product_ids

def read_file(file_path):
    with open(file_path, "r") as file:
        data = json.loads(file.read())
        products = data['Products']
    return products

def get_line_numbers(products, item_product_ids):
    lines = []
    for item_product_id in item_product_ids:
        counter = 0
        for product in products:
            p_id = product["item_product_id"]
            p_id = int(p_id)
            if p_id == item_product_id:
                if product["line_number"] != 0:
                    lines.append(product["line_number"])
                else:
                    lines.append(get_line_number(product))
                
                counter += 1

    return lines

def get_line_number(product):
    mine = product["product_id"]
    new_path = folder_path + catalog_name
    catalog = read_file(new_path)
    item = "No line number"
    for product in catalog:
        if product['product_id'] == mine:
            item = product['description']
    return item


def get_uid_and_products(id: str, date: str):
    file_path = folder_path + "/"+date+"/"+date+"_client_" + id + "_statistics.json"
    
    global catalog_name
    global products_name
    catalog_name = catalog_name.replace("date", date)
    products_name = products_name.replace("date", date)

    items = get_items(file_path)
    item_product_ids = get_item_product_ids(items)

    file_path = folder_path + products_name
    products = read_file(file_path)
    line_numbers = get_line_numbers(products, item_product_ids)

    dictionary = dict(zip(item_product_ids, line_numbers))
    return dictionary

def get_uuid_and_products_by_query(id: str, date: str):
    try:
        # Execute query with the provided contact_id using db_manager
        query = """
            SELECT
                sd.unique_id,
                sd.msisdn
            FROM
                gatchan.users u
                    JOIN
                gatchan.sales s ON u.uuid = s.user_id
                    JOIN
                gatchan.sale_details sd ON s.id = sd.sale_id
            WHERE
                u.contact_id = %s
                AND sd.status = 1
                AND sd.deactivation_date IS NULL
            ORDER BY
                s.id, sd.id;
        """
        
        results = db_manager.execute_query(query, (int(id),))
        
        # Convert query results to dictionary with unique_id as key and msisdn as value
        result_dict = {str(result[0]): result[1] for result in results} if results else {}
        return result_dict
    except Exception as e:
        return get_uid_and_products(id, date)  # Fallback to file-based method
