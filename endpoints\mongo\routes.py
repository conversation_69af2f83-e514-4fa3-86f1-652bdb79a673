from pymongo import MongoClient
from bson.objectid import ObjectId
import os
import json
import time
from fastapi import FastAPI, HTTPException, Depends, Request, Response
from pydantic import BaseModel
from typing import Optional, List
from core.utils import requires_auth
from endpoints.user.user import *
from main import app
from fastapi.responses import FileResponse
from pymongo.errors import PyMongoError
from core.logs import logger

class MongoDBClient:
    def __init__(self, db_name, collection_name, host='mongodb', port=27017, username='root', password='password'):
        self.client = MongoClient(host=host, port=port, username=username, password=password)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

    def upload_document(self, document):
        return self.collection.insert_one(document).inserted_id

    def download_document(self, document_id):
        return self.collection.find_one({'_id': document_id})

    def upload_document(self, document, content_type, timestamp, directory):
        document.update({'content_type': content_type, 'timestamp': timestamp, 'directory': directory})
        return self.collection.insert_one(document).inserted_id

    def upload_directory(self, directory_path):
        for root, dirs, files in os.walk(directory_path):
            for filename in files:
                file_path = os.path.join(root, filename)
                directory = os.path.relpath(root, directory_path)  # get relative path as directory
                content_type = os.path.splitext(file_path)[1][1:]  # get file extension as content type
                timestamp = time.time()  # get current time as timestamp
                with open(file_path, 'rb') as file:  # open in binary mode
                    content = file.read()
                    self.upload_document({'filename': filename, 'content': content}, content_type, timestamp, directory)

    def find_documents(self, directory):
        return self.collection.find({'directory': directory})

    def delete_document(self, document_id):
        self.collection.delete_one({'_id': ObjectId(document_id)})
        return document_id


# @app.get('/mongo/upload_directory')
# async def upload_directory():
#     client = MongoDBClient('gatchan', 'zendesk')
#     try:
#         dir = "zendesk"
#         client.upload_directory(dir)
#     except Exception as e:
#         raise HTTPException(status_code=400, detail=f"Error uploading directory: {e}")

#     return {"message": "Directory uploaded successfully"}
    

@app.get('/mongo/user/{directory}')
async def get_user_files(directory: str):
    return "-"
    client = MongoDBClient('gatchan', 'zendesk')
    try:
        documents = client.find_documents(directory)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Directory not found: {directory}")

    document_list = []
    for document in documents:
        document_list.append({'_id': str(document['_id']), 'filename': document['filename'], 'directory': document['directory']})

    return document_list

@app.get("/mongo/user/download/{document_id}")
async def download_user_file(document_id: str):
    return "-"

    try:
        client = MongoDBClient('gatchan', 'zendesk')
    except PyMongoError as e:
        logger("ERROR", f"Error connecting to MongoDB: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

    document = client.download_document(ObjectId(document_id))
    if document is None:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if 'content' key exists in the document
    if 'content' not in document:
        raise HTTPException(status_code=400, detail="No content in the document")

    # Get the MIME type from the document, or use a default
    mime_type = document.get('mime_type', 'application/octet-stream')

    # Get the filename from the document
    filename = document.get('filename', 'default')

    # Return the content as a response with the correct MIME type and filename
    headers = {"Content-Disposition": f"attachment; filename={filename}"}
    return Response(content=document['content'], media_type=mime_type, headers=headers)