# SQL Query Documentation 📚

## Overview
This document provides a structured overview of various SQL queries used to interact with a database management system. These queries are designed to retrieve specific data from a database with tables such as `users`, `sales`, `items`, `bundles`, and more.

---

## Query Descriptions

### 1. Retrieve User Information
```sql
SELECT uuid, first_name, last_name, mobile_phone FROM users;
```
- **Purpose**: Fetches user UUID, first name, last name, and mobile phone number.
- **Tables Used**: `users`

### 2. Get Sales Information
```sql
SELECT s.id, s.user_id, u.first_name, u.last_name, s.date 
FROM sales s 
JOIN users u ON s.user_id = u.uuid;
```
- **Purpose**: Retrieves sales ID, user UUID, first name, last name, and the date of the sale.
- **Tables Used**: `sales`, `users`

### 3. Bundle Sales Information
```sql
SELECT s.id, s.user_id, u.first_name, u.last_name, s.date 
FROM sales s 
JOIN sale_bundles sb ON s.id = sb.sale_id 
JOIN users u ON s.user_id = u.uuid;
```
- **Purpose**: Gathers information about sales that include bundles.
- **Tables Used**: `sales`, `sale_bundles`, `users`

### 4. Specific Sale Items Details
```sql
SELECT sd.item_id, i.name, 'Sale' as type 
FROM sale_details sd 
JOIN items i ON sd.item_id = i.id 
WHERE sd.sale_id = 3  
UNION ALL  
SELECT bd.item_id, i.name, 'Bundle' as type 
FROM sale_bundles sb 
JOIN bundle_details bd ON sb.bundle_id = bd.bundle_id 
JOIN items i ON bd.item_id = i.id 
WHERE sb.sale_id = 3;
```
- **Purpose**: Fetches detailed information about specific items in a sale, including bundles and individual items.
- **Tables Used**: `sale_details`, `items`, `sale_bundles`, `bundle_details`

### 5. User Info From a Sale
```sql
SELECT u.uuid, u.first_name, u.last_name, u.mobile_phone 
FROM sales s 
JOIN users u ON s.user_id = u.uuid 
WHERE s.id = 3;
```
- **Purpose**: Retrieves user information based on a specific sale.
- **Tables Used**: `sales`, `users`

### 6. All Items Information
```sql
SELECT id, name, price, provider FROM items;
```
- **Purpose**: Lists all items, including their ID, name, price, and provider.
- **Tables Used**: `items`

### 7. Specific Item Information
```sql
SELECT * FROM items WHERE id = 339;
```
- **Purpose**: Provides detailed information about a specific item.
- **Tables Used**: `items`

### 8. All Bundle Information
```sql
SELECT b.id, b.name, SUM(i.price) as total_price 
FROM bundles b 
JOIN bundle_details bd ON b.id = bd.bundle_id 
JOIN items i ON bd.item_id = i.id 
GROUP BY b.id, b.name;
```
- **Purpose**: Gathers information on all bundles, including total price.
- **Tables Used**: `bundles`, `bundle_details`, `items`

### 9. Specific Bundle Information
```sql
SELECT b.id, b.name, SUM(i.price) as total_price 
FROM bundles b 
JOIN bundle_details bd ON b.id = bd.bundle_id 
JOIN items i ON bd.item_id = i.id 
WHERE b.id = 114 
GROUP BY b.id, b.name;
```
- **Purpose**: Retrieves details of a specific bundle, including the total price.
- **Tables Used**: `bundles`, `bundle_details`, `items`

### 10. Items in a Specific Bundle
```sql
SELECT i.id, i.name 
FROM bundle_details bd 
JOIN items i ON bd.item_id = i.id 
WHERE bd.bundle_id = 114;
```
- **Purpose**: Lists all items within a specific bundle.
- **Tables Used**: `bundle_details`, `items`

---

## Database Schema: DBML

### Users Table
- UUID (Primary Key)
- Contact ID
- First Name, Last Name, Second Last Name
- National ID, Mobile Phone, Email
- Address, Postal Code, City, Province
- Billing ID (Foreign Key)
- Status, IsB2B (Boolean)
- Provider ID (Foreign Key)

### Provider Table
- ID (Primary Key)
- Name

### Billing Table
- ID (Primary Key)
- Bank Account
- Address, Postal Code, City, Province

### Items Table
- ID (Primary Key)
- Item ID
- Name, Description
- Pricing and Recurrence Details
- Product and Line

 Type
- Provider
- Status, IsFiltered (Boolean)

### Sales Table
- ID (Primary Key)
- User ID (Foreign Key)
- Date

### Sale Details Table
- ID (Primary Key)
- Sale ID, Item ID (Foreign Keys)
- IP, MSISDN

### Bundles Table
- ID (Primary Key)
- Name

### Bundle Details Table
- ID (Primary Key)
- Bundle ID, Item ID (Foreign Keys)

### Sale Bundles Table
- ID (Primary Key)
- Sale ID, Bundle ID (Foreign Keys)