from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(docs_url=None, redoc_url=None)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,  # Allows credentials (cookies, headers, etc.)
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

from endpoints.auth.routes import *
from endpoints.openai.routes import *
from endpoints.invoices.routes import *
from endpoints.statistics.routes import *
from endpoints.firewall.routes import *
from endpoints.crm.routes import *
from endpoints.user.routes import *
from endpoints.mongo.routes import *

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)