import imaplib
import email
from email.header import decode_header
import re
from decouple import config
from core.logs import logger

def login_to_mail(username, password):
    mail = imaplib.IMAP4_SSL("imap.gmail.com")
    mail.login(username, password)
    return mail

def select_mailbox(mail, mailbox):
    mail.select(mailbox)

def get_all_mail_ids(mail):
    result, data = mail.uid('search', None, "ALL")
    mail_ids = data[0]
    id_list = mail_ids.split()
    return id_list

def fetch_email_body(mail, email_id):
    result, email_data = mail.uid('fetch', email_id, '(BODY[TEXT])')
    raw_email = email_data[0][1].decode("utf-8")
    email_message = email.message_from_string(raw_email)
    return email_message

def get_payload(email_message):
    if email_message.is_multipart():
        for payload in email_message.get_payload():
            if payload.get_content_type() in ["text/plain", "text/html"]:
                return payload.get_payload()
    else:
        return email_message.get_payload()

def get_sender_email(email_message):
    from_header = email.utils.getaddresses([email_message.get("From", "")])
    if from_header:
        return from_header[0][1]
    else:
        return None

def get_email_subject(mail, email_id):
    result, email_data = mail.uid('fetch', email_id, '(BODY[HEADER.FIELDS (SUBJECT)])')
    raw_email = email_data[0][1].decode("utf-8")
    email_message = email.message_from_string(raw_email)
    subject = decode_header(email_message['Subject'])[0][0]
    if isinstance(subject, bytes):
        try:
            subject = subject.decode('utf-8')
        except UnicodeDecodeError:
            subject = subject.decode('ISO-8859-1')
    return subject

# def get_all_email_subjects(mail, id_list):
#     return [{"id": email_id, "subject": get_email_subject(mail, email_id)} for email_id in id_list[-50:]]

def get_all_email_subjects(mail, id_list, start, end):
    reversed_id_list = id_list[::-1]  # Reverse the list
    return [{"id": email_id, "subject": get_email_subject(mail, email_id)} for email_id in reversed_id_list[start:end]]

def get_emails_(range_index = 0):
    username = config('EMAIL')
    password = config('EMAIL_PASSWORD')
    mailbox = "INBOX"

    mail = login_to_mail(username, password)
    select_mailbox(mail, mailbox)

    all_ids = get_all_mail_ids(mail)

    range_index = int(range_index)

    start = (range_index - 1) * 50
    end = range_index * 50

    if range_index == 0:
        start = 0
        end = 50
        print("Hi im 0")

    return get_all_email_subjects(mail, all_ids, start, end)


def get_email_(email_id):
    username = config('EMAIL')
    password = config('EMAIL_PASSWORD')
    mailbox = "INBOX"

    email_id = str(email_id)

    mail = login_to_mail(username, password)
    select_mailbox(mail, mailbox)
    email_message = fetch_email_body(mail, email_id)
    email_content = get_payload(email_message)

    return {"id": email_id, "subject": get_email_subject(mail, email_id), "body": email_content}









import os.path
import base64
import json
import re
from datetime import datetime
from email.utils import parsedate_to_datetime
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

SCOPES = ['https://www.googleapis.com/auth/gmail.readonly','https://www.googleapis.com/auth/gmail.modify']

def get_credentials():
    creds = None
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                "credentials.json", SCOPES
            )
            creds = flow.run_local_server(port=0)
    with open("token.json", "w") as token:
        token.write(creds.to_json())
    return creds

def get_email_body(payload):
    if 'parts' in payload:
        return get_email_body(payload['parts'][0])
    if 'data' in payload['body']:
        data = payload['body']['data']
        data = data.replace("-","+").replace("_","/")
        decoded_data = base64.b64decode(data)
        str_text = str(decoded_data, "utf-8")
        return str_text
    return ""

def get_email_data(service, message):
    email_info = {}
    msg = service.users().messages().get(userId='me', id=message['id']).execute()                
    email_data = msg['payload']['headers']
    for values in email_data:
        name = values['name']
        if name == 'From':
            from_str = values['value']
            match = re.search(r'<(.+)>', from_str)
            if match:
                email_info['From'] = match.group(1)
        if name == 'Date':
            date_str = values['value']
            date_obj = parsedate_to_datetime(date_str)
            formatted_date = date_obj.strftime('%Y%m%d %H%M%S')
            email_info['Date'] = formatted_date
        if name == 'Subject':
            email_info['Subject'] = values['value']

    email_info['Body'] = get_email_body(msg['payload'])
    email_info['threadId'] = msg['threadId']
    email_info['id'] = msg['id']
    email_info['historyId'] = msg['historyId']
    return email_info, msg

def get_all_emails(service, email_address):
    all_emails = []
    results = (
        service.users()
        .messages()
        .list(userId="me", labelIds=["INBOX"])
        .execute()
    )
    messages = results.get("messages", [])
    if not messages:
        print("No new emails found")
    else:
        for message in messages:
            email_info, msg = get_email_data(service, message)
            if email_info['From'] == email_address:
                all_emails.append(email_info)
    return all_emails

def remove_duplicate_content(all_emails):
    def find_overlapping_content(email1, email2):
        """Compares two email bodies and returns the indices in email1 
           where content overlaps with email2"""
        overlap_indices = []
        max_overlap_len = 0  # Track length of longest overlap

        for i in range(len(email1)):
            for j in range(len(email2)):
                # Check for overlaps of increasing length
                length = 0
                while i + length < len(email1) and \
                      j + length < len(email2) and \
                      email1[i + length] == email2[j + length]:
                    length += 1
                if length > max_overlap_len:
                    overlap_indices = [i, i + length]
                    max_overlap_len = length

        return overlap_indices

    thread_id = ""
    body_last = ""
    for i in range(len(all_emails) - 1, -1, -1):  # Changed here
        if all_emails[i]['threadId'] != thread_id:
            thread_id = all_emails[i]['threadId']
            body_last = all_emails[i]['Body']
        else:
            overlap = find_overlapping_content(all_emails[i]['Body'], body_last)
            if overlap:
                start, end = overlap
                all_emails[i]['Body'] = all_emails[i]['Body'][:start]
                print("Duplicate content removed:", all_emails[i]['Body'][start:end])

            body_last = all_emails[i]['Body']

    return all_emails


def main_email(email_address):
    creds = get_credentials()
    try:
        service = build('gmail', 'v1', credentials=creds)
        all_emails = get_all_emails(service, email_address)

        try:
            all_emails = remove_duplicate_content(all_emails)
        except Exception as e:
            print("Error removing duplicate content: ", e)


        return all_emails
    except Exception as e:
        print("Error getting emails: ", e)




from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
from email.mime.text import MIMEText
import base64

def get_service():
    creds = Credentials.from_authorized_user_file('token.json')
    service = build('gmail', 'v1', credentials=creds)
    return service

def get_email(service, email_id):
    email = service.users().messages().get(userId="me", id=email_id).execute()
    return email

def get_thread_id(email):
    thread_id = email['threadId']
    return thread_id

def get_references(email):
    references = next((header['value'] for header in email['payload']['headers'] if header['name'] == 'References'), None)
    return references

def create_raw_message(references, your_email_address, body_content):
    raw_message = (
        f"Subject: Re: Previous Email\n"
        f"In-Reply-To: {references}\n"
        f"References: {references}\n"
        f"To: {your_email_address}\n"
        f"From: {your_email_address}\n"
        f"\n"
        f"{body_content}"
    )
    return raw_message

def create_body(raw_message, thread_id):
    body = {
        "raw": base64.urlsafe_b64encode(raw_message.encode("utf-8")).decode("utf-8"),
        'threadId': thread_id
    }
    return body

def reply_to_email(your_email_address, email_id, body_content):
    creds = get_credentials()
    service = build('gmail', 'v1', credentials=creds)
    email = get_email(service, email_id)
    thread_id = get_thread_id(email)
    references = get_references(email)
    raw_message = create_raw_message(references, your_email_address, body_content)
    body = create_body(raw_message, thread_id)
    # Here you can send the email using the 'body' and 'service'
    service.users().messages().send(userId="me", body=body).execute()