from fastapi import HTTPException, Depends, Request, Response
from pydantic import BaseModel
from typing import Optional, List
from core.utils import requires_auth
from endpoints.user.user import *
from main import app

@app.post("/user")
async def user(user=Depends(requires_auth)):
    uuid = user[0]
    res = get_user_by_uuid(uuid)
    return res

@app.post("/user/contact-id")
async def user_contact_id(user=Depends(requires_auth)):
    uuid = user[0]
    res = get_user_contact_id(uuid)
    return res