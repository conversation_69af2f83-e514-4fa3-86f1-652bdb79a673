from datetime import datetime
from dotenv import load_dotenv
import json
import os

load_dotenv()

lOGS_PATH = os.getenv("LOGS_PATH", "logs")
BACKUPS_PATH = os.getenv("BACKUPS_PATH", "backups")

def logger(type: str, message: str):
    """
    Logs a message with a specified type to a daily log file.

    This function creates a log file named with today's date if it doesn't exist,
    and appends a log entry to it. The log entry includes the current timestamp,
    the type of log, and the log message.

    Parameters:
    - type (str): The type of log entry (e.g., 'info', 'error', 'warning').
    - message (str): The message to log.

    The log is stored in a file within the 'logs' directory, and each log file is named
    with the date on which the log was recorded.

    Note: The LOGS_PATH should be defined in your application's configuration.
    """
    if not os.path.exists("logs"):
        os.mkdir("logs")
    
    # date = datetime.now().strftime("%d-%m-%Y")
    date = datetime.now().strftime("%Y-%m-%d")

    with open(f"{lOGS_PATH}/{date}.log", "a") as file:
        file.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [{type.upper()}]: {message}\n")

def saveBackup(data, typeBackup):
    """
    Saves a backup of a specific type ('schedule', 'address-objects', 'access-rules') 
    into a timestamped file in a directory specified by BACKUPS_PATH.

    Parameters:
    - typeBackup (str): Type of the backup to be saved.

    Creates a dated directory if it doesn't exist and logs the backup process. 
    In case of errors, logs an error message.
    """
    try:
        now = datetime.now()
        filename = "backup_" + now.strftime("%d-%m-%Y_%H-%M-%S") + "_" + typeBackup

        if not os.path.exists(os.path.join(BACKUPS_PATH, now.strftime("%d-%m-%Y"))):
            os.makedirs(os.path.join(BACKUPS_PATH, now.strftime("%d-%m-%Y")))

        path = os.path.join(BACKUPS_PATH, now.strftime("%d-%m-%Y"), filename + ".json")

        if typeBackup.lower() == 'schedule':
            with open(path, 'w') as outfile:
                json.dump(data, outfile)
            logger("Backup", "Backup de schedule create Name: " + str(filename) + ".log")
        elif typeBackup.lower() == 'address-objects':
            with open(path, 'w') as outfile:
                json.dump(data, outfile)
            logger("Backup", "Backup de address-objects create Name: " + str(filename) + ".log")
        elif typeBackup.lower() == 'access-rules':
            with open(path, 'w') as outfile:
                json.dump(data, outfile)
            logger("Backup", "Backup de access-rules create Name: " + str(filename) + ".log")
    except Exception as e:
        logger("error", "Failed to save backup: " + str(e))