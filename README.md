# 🚀 GatchanApi

## 📖 Índice

- [🔧 Instalación](#🔧-instalación)
- [🚀 Uso](#🚀-uso)

## 🔧 Instalación

Sigue estos pasos para instalar y ejecutar el proyecto en tu entorno local:

1. **Clonar el repositorio**:
```sh
git clone https://github.com/GatchanWorkspace/GatchanApi.git
cd GatchanApi
```

2. **Crear un entorno virtual** (opcional, pero recomendado):
```sh
python -m venv .venv
.venv\Scripts\activate.bat
```

3. **Instalar las dependencias**:
```sh
pip install -r requirements.txt
```

4. 🎉 Una vez finalizada la instalación, puedes seguir las instrucciones de la sección de [🚀 Uso](#uso) para ejecutar el proyecto.

## 🚀 Uso

```python
uvicorn main:app --reload
```

## 📝 .env

```sh
# JWT settings
SECRET_KEY=
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=11520 # 8 days
SAL_CODE=

# Database settings
DB_HOST=
DB_USER=
DB_PASSWORD=
DB_NAME=

# Core
LOGS_PATH="logs"
```