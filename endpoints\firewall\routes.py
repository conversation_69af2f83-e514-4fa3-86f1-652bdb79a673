from fastapi import Header, HTTPException, Depends, Request
from endpoints.firewall.disableData import *
from core.firewall.createObjects import *
from core.utils import requires_auth
from main import app
import asyncio
import random

@app.post("/firewall/disabledata")
async def disableData(request: Request = None):
    try:
        data = await request.json()

        data = data['data']['dataFirewall']

        logger("Data", f"Data received: {data}")

        # Obtenemos todos los objetos del firewall
        all_objects = await get_all_objects()

        if all_objects is None:
            raise HTTPException(status_code=500, detail="Error while getting objects from the firewall")
        
        # Obtenemos la informacion del usuario de la base de datos
        user_info = get_user_info(data['unique_id'])

        # Obtenemos los objetos del usuario
        objects = getObjectsByUnique_id(all_objects, data['unique_id'])

        if objects is None:
            raise HTTPException(status_code=500, detail="Error while getting objects by unique_id")
        
        # Hacemos un backup de los objetos del firewall antes de modificarlos
        saveBackup(all_objects["access-rules/ipv4"], "access-rules")
        saveBackup(all_objects["address-objects/ipv4"], "address-objects")
        saveBackup(all_objects["schedules"], "schedule")

        # comprobamos si el usuario ya tiene una regla de acceso address object creada
        # si no la tiene, la creamos
        isCreatedAddressObject = False
        if len(objects['address_objects']) == 0:
            # Creamos el address object
            address_object = create_address_object(user_info['ip'], "address_object_" + str(data['unique_id']), user_info['product_type'])
            logger("creada", f"address_object: {address_object}")
            if address_object is None:
                logger("error", "Error while creating address object")
                raise HTTPException(status_code=500, detail="Error while creating address object")
            isCreatedAddressObject = True
        else:
            # Si ya tiene una regla de acceso address object creada, la reutilizamos pero comprobamos si la dirección IP ha cambiado y si es así la actualizamos
            address_object = objects['address_objects'][list(objects['address_objects'].keys())[0]]
            if address_object['ipv4']['host']['ip'] != user_info['ip']:
                # Borramos el address object
                if not delete_address_object(address_object["address_objects"][0]["ipv4"]["name"]):
                    logger("error", "Error while deleting address object")
                    raise HTTPException(status_code=500, detail="Error while deleting address object")
                
                # Creamos el address object
                address_object = create_address_object(user_info['ip'], "address_object_" + str(data['unique_id']), user_info['product_type'])
                if address_object is None:
                    logger("error", "Error while creating address object after deleting")
                    raise HTTPException(status_code=500, detail="Error while creating address object")
                isCreatedAddressObject = True
                
        # Creamos el schedule pero en el nombre ponemos un identificador único para poder tener mas de uno con el unique_id como 5 caracteres y numeros aleatorios para que sea único
        schedule = create_schedule(data, "schedule_" + str(data['unique_id']) + "_" + str(random.randint(10000, 99999)))
        logger("creada", f"schedule: {schedule}")

        if schedule is None:
            logger("info", "schedule is None")
            raise HTTPException(status_code=500, detail="Error while creating schedule")
        
        # Creamos la regla de acceso
        ruleName = "Redapp_" + str(data['unique_id']) + "_" + str(random.randint(10000, 99999))
        schedule_name = schedule['scheduler']['schedule'][0]['name']
        addressName = find_ipv4_name(address_object)

        if addressName is None:
            logger("error", "No valid 'ipv4' structure found in address_object")
            raise HTTPException(status_code=500, detail="No valid 'ipv4' structure found in address_object")

        comment = data['comment'] if 'comment' in data else None

        access_rule = create_rule(ruleName, addressName, schedule_name, comment, user_info['product_type'])

        if access_rule is None:
            logger("error", "Error while creating access rule")
            raise HTTPException(status_code=500, detail="Error while creating access rule")
        
        sendFirewallObjects = {
            "access_rule": access_rule,
            "schedule": schedule,
        }


        # comprobamos si se creo el address object no lo mandamos al firewall pero si se creo si lo mandamos
        if isCreatedAddressObject:
            sendFirewallObjects['address_object'] = address_object

        r = await SaveDisableData(sendFirewallObjects)
        
        # Devolvemos la información de la regla de acceso
        return {
            "user_info": user_info,
            "id_unique": data['unique_id'],
            "access_rule": access_rule,
            "schedule": schedule,
            "address_object": address_object,
            "response_firewall": r
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/firewall/panicdata/{unique_id}")
async def panicData(unique_id: int):
    try:
        # Obtenemos todos los objetos del firewall
        all_objects = await get_all_objects()

        if all_objects is None:
            logger("error", "Error while getting objects from the firewall")
            raise HTTPException(status_code=500, detail="Error while getting objects from the firewall")
        
        # Obtenemos la informacion del usuario de la base de datos
        user_info = get_user_info(unique_id)

        # Obtenemos los objetos del usuario
        objects = getObjectsByUnique_id(all_objects, unique_id)

        if objects is None:
            logger("error", "Error while getting objects by unique_id")
            raise HTTPException(status_code=500, detail="Error while getting objects by unique_id")
        
        # Hacemos un backup de los objetos del firewall antes de modificarlos
        saveBackup(all_objects["access-rules/ipv4"], "access-rules")
        saveBackup(all_objects["address-objects/ipv4"], "address-objects")
        saveBackup(all_objects["schedules"], "schedule")

        # comprobamos si el usuario ya tiene una regla de acceso address object creada
        if len(objects['address_objects']) == 0:
            # Creamos el address object
            address_object = create_address_object(user_info['ip'], "address_object_" + str(unique_id), user_info['product_type'])
            logger("info", f"address_object: {address_object}")
            if address_object is None:
                raise HTTPException(status_code=500, detail="Error while creating address object")
        else:
            # Si ya tiene una regla de acceso address object creada, la reutilizamos pero comprobamos si la dirección IP ha cambiado y si es así la actualizamos
            address_object = objects['address_objects'][list(objects['address_objects'].keys())[0]]
            if address_object['ipv4']['host']['ip'] != user_info['ip']:
                # Borramos el address object
                if not delete_address_object(address_object["address_objects"][0]["ipv4"]["name"]):
                    raise HTTPException(status_code=500, detail="Error while deleting address object")
                
                # Creamos el address object
                address_object = create_address_object(user_info['ip'], "address_object_" + str(unique_id), user_info['product_type'])
                if address_object is None:
                    raise HTTPException(status_code=500, detail="Error while creating address object")

        addressName = find_ipv4_name(address_object)
        logger("CACAO======", f"addressName: {addressName}")

        if addressName is None:
            logger("error", "No valid 'ipv4' structure found in address_object")
            raise HTTPException(status_code=500, detail="No valid 'ipv4' structure found in address_object")
        
        # guardamos en la base de datos las reglas que tenia el usuario antes de ser deshabilitado para poder recuperarlas
        rSaveines = saveUserLines(unique_id, objects)

        logger("CACAO======", f"rSaveines: {rSaveines}")
        
        # si el usario no tiene reglas guardadas devuelve [] y no se ejecuta el siguiente codigo
        if rSaveines is not None and len(rSaveines) > 0:
            # eliminamos las reglas anteriores del usuario recorriendo rSaveines que estan las uuid de las reglas
            for uuid in rSaveines:
                r = await delete_access_rule(uuid)
                
                if r is None:
                    logger("error", "Error while deleting access rule")
                    raise HTTPException(status_code=500, detail="Error while deleting access rule")
                
                await asyncio.sleep(1)
        
        # Añadimos el address al grupo de panic_button
        rAddAddressToGroup = await addAddressToGroup(addressName, "PanicButton-Group")

        logger("CACAO======", f"rAddAddressToGroup: {rAddAddressToGroup}")

        if not rAddAddressToGroup:
            logger("error", "Error while adding address to group")
            raise HTTPException(status_code=500, detail="Error while adding address to group")
    
        # Devolvemos la información de la regla de acceso
        return {
            "user_info": user_info,
            "id_unique": unique_id,
            "address_name": addressName,
            "success": rAddAddressToGroup
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.delete("/firewall/panicdata/delete/{unique_id}")
async def deleteDisableData(unique_id: int):
    try:
        # Obtenemos todos los objetos del firewall
        all_objects = await get_all_objects()

        if all_objects is None:
            logger("error", "Error while getting objects from the firewall")
            raise HTTPException(status_code=500, detail="Error while getting objects from the firewall")
        
        # obtenesmos los objetos del usuario de la base de datos
        userLines = await getUserLines(unique_id)

        if userLines is None:
            logger("error", "Error while getting user lines")
            raise HTTPException(status_code=500, detail="Error while getting user lines")
        
        # Hacemos un backup de los objetos del firewall antes de modificarlos
        saveBackup(all_objects["access-rules/ipv4"], "access-rules")
        saveBackup(all_objects["address-objects/ipv4"], "address-objects")
        saveBackup(all_objects["schedules"], "schedule")

        # BUSCAMOS EN ALL OBJECTS EL ADDRESS OBJECT QUE TENGA EL UNIQUE_ID DEL USUARIO
        objects = getObjectsByUnique_id(all_objects, unique_id)

        if objects is None:
            logger("error", "Error while getting objects by unique_id")
            raise HTTPException(status_code=500, detail="Error while getting objects by unique_id")
        
        # Obtenemos el nombre del address object
        addressName = find_ipv4_name(objects['address_objects'][list(objects['address_objects'].keys())[0]])

        rRemoveAddressFromGroup = await removeAddressFromGroup(addressName, "PanicButton-Group")

        if not rRemoveAddressFromGroup:
            logger("error", "Error while removing address from group")
            raise HTTPException(status_code=500, detail="Error while removing address from group")
        
        if userLines is not None and len(userLines) > 0:
            r = await createAccessRule(userLines)
            if r is None:
                logger("error", "Error while creating access rule")
                raise HTTPException(status_code=500, detail="Error while creating access rule")

            # eliminamos las reglas anteriores de la base de datos
            rDeleteUserLines = await deleteUserLines(unique_id)

            if not rDeleteUserLines:
                logger("error", "Error while deleting user lines")
                raise HTTPException(status_code=500, detail="Error while deleting user lines")
            
        return {
            "success": rRemoveAddressFromGroup
        }

    except Exception as e:
        logger("error", f"Exception occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/firewall/disabledata/delete")
async def deleteDisableData(request: Request = None):
    try:
        data = await request.json()

        if data.get('uuid') is None:
            raise HTTPException(status_code=500, detail="UUID is missing in request data")

        all_objects = await get_all_objects()

        if all_objects is None:
            raise HTTPException(status_code=500, detail="Error while getting objects from the firewall")

        r = await DeleteDisableDataObjects(all_objects, data['uuid'])
        if r is None:
            raise HTTPException(status_code=500, detail="Error while deleting objects from the firewall")

        # Guardar respaldos antes de realizar las eliminaciones
        saveBackup(all_objects["access-rules/ipv4"], "access-rules")
        saveBackup(all_objects["address-objects/ipv4"], "address-objects")
        saveBackup(all_objects["schedules"], "schedule")
        
        access_rule_delete_success = await delete_access_rule(data['uuid'])
        if not access_rule_delete_success:
            raise HTTPException(status_code=500, detail="Error while deleting access rule")

        # Llamar a delete_schedule y delete_access_rule de manera asíncrona
        schedule_delete_success = await delete_schedule(r['schedule'])
        if not schedule_delete_success:
            raise HTTPException(status_code=500, detail="Error while deleting schedule")
        
        return {
            "access_rule": access_rule_delete_success,
            "schedule": schedule_delete_success,
            "response_firewall": r
        }

    except Exception as e:
        logger("error", f"Exception occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/firewall/disabledata/get")
async def getDisableData(authorization: str = Header(...)):
    token = None
    try:
        scheme, token = authorization.split()
        # print the token
        print(token)
        if scheme.lower() != "bearer":
            raise HTTPException(status_code=401, detail="Invalid authentication scheme")
    except ValueError:
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    if not token:
        raise HTTPException(status_code=401, detail="Token not provided")
    
    from endpoints.auth.login import decode_token
    user_uuid = decode_token(token)["UUID"]

    try:
        userLines = await get_user_lines(user_uuid)
        if userLines is None:
            raise HTTPException(status_code=500, detail="Error while getting user lines")

        all_objects = await get_all_objects()
        if all_objects is None:
            raise HTTPException(status_code=500, detail="Error while getting objects from the firewall")

        objects = []
        for line in userLines:
            obj = getObjectsByUnique_id(all_objects, line['unique_id'])
            if obj:
                objects.append(obj)

        import json
        import re
        # Convert objects to a json
        objects_json = json.dumps(objects, indent=4)
        logger("info", f"Fakku you i reached here")

        def format_rules(rules):
            """
            Format the given rules.

            Args:
                rules (list): A list of dictionaries where each dictionary represents a rule.

            Returns:
                list: A list of all formatted rules.
            """

            all_rules_formatted = []

            logger("1", f"b4 4 loop")

            for rule in rules:
                if not rule['access_rules']:
                    logger("2", f"b4 continue")
                    continue

                for access_rule in rule['access_rules']:
                    logger("3", f"b4 unique_id")
                    unique_id = access_rule['ipv4']['name']
                    unique_id = re.search(r'_(.*?)_', unique_id)
                    if not unique_id:
                        continue

                    sc_name = access_rule['ipv4']['schedule']['name']

                    schedule_content = rule['schedules'][sc_name]
                    if not schedule_content:
                        continue

                    logger("4", f"b4 schedule_content checkpoint")

                    schedule_content = schedule_content["occurs"]["recurring"]["recurring"]
                    schedule_content_len = len(schedule_content)

                    days_content = {
                        "lunes":False,
                        "martes":False,
                        "miercoles":False,
                        "jueves":False,
                        "viernes":False,
                        "sabado":False,
                        "domingo":False
                    }

                    horaInicio = ""
                    horaFin = ""

                    logger("5", f"b4 schedule_content_len checkpoint")

                    if schedule_content_len == 0:
                        continue
                    elif schedule_content_len == 1:
                        schedule = schedule_content[0]
                        days_content["lunes"] = schedule.get("mon", False)
                        days_content["martes"] = schedule.get("tue", False)
                        days_content["miercoles"] = schedule.get("wed", False)
                        days_content["jueves"] = schedule.get("thu", False)
                        days_content["viernes"] = schedule.get("fri", False)
                        days_content["sabado"] = schedule.get("sat", False)
                        days_content["domingo"] = schedule.get("sun", False)

                        horaInicio = schedule.get("start", "")
                        horaFin = schedule.get("end", "")
                    else:
                        horaInicio = schedule_content[0].get("start", "")
                        horaFin = schedule_content[-1].get("end", "")
                        
                        for schedule in schedule_content:
                            days_content["lunes"] = days_content.get("lunes", False) or schedule.get("mon", False)
                            days_content["martes"] = days_content.get("martes", False) or schedule.get("tue", False)
                            days_content["miercoles"] = days_content.get("miercoles", False) or schedule.get("wed", False)
                            days_content["jueves"] = days_content.get("jueves", False) or schedule.get("thu", False)
                            days_content["viernes"] = days_content.get("viernes", False) or schedule.get("fri", False)
                            days_content["sabado"] = days_content.get("sabado", False) or schedule.get("sat", False)
                            days_content["domingo"] = days_content.get("domingo", False) or schedule.get("sun", False)

                    checkAllHours = False
                    checkAllDays = False

                    logger("6", f"b4 if checkpoint")

                    if horaInicio == "00:00" and horaFin == "23:59":
                        checkAllHours = True
                    elif checkAllHours:
                        horaInicio = "00:00"
                        horaFin = "23:59"

                    if days_content["lunes"] and days_content["martes"] and days_content["miercoles"] and days_content["jueves"] and days_content["viernes"] and not days_content["sabado"] and not days_content["domingo"]:
                        checkAllDays = True

                    access_rule_uuid = access_rule['ipv4']['uuid']

                    def get_msisdn_by_unique_id(userLines):
                        id_to_msisdn = {line['unique_id']: line['msisdn'] for line in userLines}
                        
                        if not id_to_msisdn:
                            logger("error", "Error while getting msisdn by unique_id")
                            raise HTTPException(status_code=500, detail="Error while getting msisdn by unique_id")
                        
                        logger("7", f"b4 return id_to_msisdn")

                        return id_to_msisdn
                    
                    logger("7", f"b4 get_msisdn_by_unique_id")
                    
                    id_to_msisdn = get_msisdn_by_unique_id(userLines)

                    logger("8", f"b4 rule_object")

                    logger("rule", f"id {id_to_msisdn}")

                    unique_id_int = int(unique_id.group(1))
                    
                    logger("rule", f"msisdn fdskjfsdl {id_to_msisdn[unique_id_int]}")
                    logger("rule", f"horaInicio fdskjfsdl {horaInicio}")
                    logger("rule", f"horaFin fdskjfsdl {horaFin}")
                    logger("rule", f"checkAllHours fdskjfsdl {checkAllHours}")
                    logger("rule", f"checkAllDays fdskjfsdl {checkAllDays}")
                    logger("rule", f"unique_id fdskjfsdl {unique_id.group(1)}")
                    logger("rule", f"uuid fdskjfsdl {access_rule_uuid}")
                    logger("rule", f"comment fdskjfsdl {access_rule['ipv4']['comment']}")
                    logger("rule", f"dias fdskjfsdl {days_content}")

                    rule_object = {            
                        "msisdn":id_to_msisdn[unique_id_int],
                        "horaInicio":horaInicio,
                        "horaFin":horaFin,
                        "checkAllHours":checkAllHours,
                        "checkAllDays":checkAllDays,
                        "unique_id":unique_id_int,
                        "uuid":access_rule_uuid,
                        "comment":access_rule['ipv4']['comment'],
                        "dias": days_content
                    }

                    logger("9", f"b4 all_rules_formatted.append")

                    all_rules_formatted.append(rule_object)

            logger("10", f"b4 return")

            return all_rules_formatted

        result = format_rules(json.loads(objects_json))

        logger("🥶", f"sheeeesh")

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/firewall/pandata/get")
async def getPanicData(authorization: str = Header(...)):
    token = None
    try:
        scheme, token = authorization.split()
        # print the token
        print(token)
        if scheme.lower() != "bearer":
            raise HTTPException(status_code=401, detail="Invalid authentication scheme")
    except ValueError:
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    if not token:
        raise HTTPException(status_code=401, detail="Token not provided")
    
    from endpoints.auth.login import decode_token
    user_uuid = decode_token(token)["UUID"]

    try:
        userLines = await get_user_lines(user_uuid)
        logger("userLines", f"userLines: {userLines}")

        if userLines is None:
            logger("error", "Error while getting user lines")
            raise HTTPException(status_code=500, detail="Error while getting user lines")
        
        # agregamos manuel al userLines uan nueva que no esta en la base de datos
        # userLines.append({
        #     "unique_id": 11111,
        #     "msisdn": "1234567890"
        # })

        groupContent = await getAddressesFromGroup("PanicButton-Group")

        if groupContent is None:
            logger("error", "Error while getting addresses from group")
            raise HTTPException(status_code=500, detail="Error while getting addresses from group")
        
        # de userLines agarramos y ponemos un formato que es el siguiente
        # [
        #     {
        #         "id": unique_id,
        #         "status": false
        #     }
        # ]
        # y lo guardamos en userLinesFormatted

        userLinesFormatted = []
        for line in userLines:
            userLinesFormatted.append({
                "id": line['unique_id'],
                "status": False
            })

        # recorremos el groupContent y si el address esta en el groupContent lo ponemos en true en userLinesFormatted
        for address in groupContent:
            for line in userLinesFormatted:
                if address == f"address_object_{line['id']}":
                    line['status'] = True

        return userLinesFormatted
    except Exception as e:
        logger("error", f"Exception occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
