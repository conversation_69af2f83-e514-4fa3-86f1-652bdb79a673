import json
import openai
from main import app
from core.openai.user import User
from endpoints.openai.chat_with_gpt import chat_with_gpt
from decouple import config

openai.api_key = config("OPENAI_API_KEY")

@app.post("/openai")
async def postai_url(message_json: dict):
    gpt_message = chat_with_gpt(message_json["message"], message_json["id"], 'rene')
    dummy = {"message": gpt_message}
    return dummy

# @app.post("/openai/ask

@app.get("/openai/history/{user_id}")
async def get_history(user_id: str):
    user: User = User(user_id, "Eres un asistente virtual")
    chat = user.get_chat_history()

    return chat

@app.get("/test")
async def test():
    return "test"