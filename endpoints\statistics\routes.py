from fastapi import HTTPException, Depends, Request
import json
import openai
import dotenv
from main import app
from core.utils import requires_auth

from endpoints.statistics.stats import get_date_stats,  yesterday, get_last_month_stats
from endpoints.statistics.products import get_uid_and_products

_id = "298913254"
_date = "20231024"

@app.get("/statistics/{id}")
async def get_statistics(user=Depends(requires_auth), id: str = "0"):
    print("Getting stats for yesterday")
    info = get_date_stats(id, yesterday())
    info = json.loads(info)
    return info

@app.get("/statistics/date/{id}/{date}")
async def get_statistics(user=Depends(requires_auth), id: str = "0", date: str = _date):
    print("Getting stats for date: " + date)
    info = get_date_stats(id, yesterday())
    info = json.loads(info)
    return info

@app.get("/statistics/month/{id}")
async def get_statistics(user=Depends(requires_auth), id: str = "0"):
    print("Getting last month stats")
    info = get_last_month_stats(id)
    info = json.loads(info)
    return info

@app.get("/uid/{id}")
async def get_uid(user=Depends(requires_auth), id: str = "0"):
    print("Getting uid and products")
    print("ID: " + id)
    print("Yesterday: " + yesterday())
    info = get_uid_and_products(id, yesterday()) 
    return info