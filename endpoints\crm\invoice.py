import pdfkit
from jinja2 import Template
import os

from db.connection import db_manager
from endpoints.crm.sales import *


def genhtml(sale_id: str):
    def generate_items(items):
        html_item = """
        <tr>
            <td>{{ item_name }}</td>
            <td>{{ item_code }}</td>
            <td>{{ item_conditions }}</td>
            <td>{{ item_price }}</td>
            <td>{{ item_quantity }}</td>
            <td>{{ item_subtotal }}</td>
        </tr>
        """
        html_content = ""
        for item in items:
            html_content += Template(html_item).render(item)
        return html_content


    try:
        # Get all sale details from the database.
        query = """
        SELECT items.name AS item_name, items.product_id AS item_code, items.price FROM gatchan.sale_details JOIN gatchan.items ON sale_details.item_id = items.id WHERE sale_details.sale_id = %s;
        """
        print(f"Executing query with sale_id: {sale_id}")
        result = db_manager.execute_query(query, (sale_id,))

        # Tuple of tuples, (name, code, Decimal('price'))
        items = []
        for item in result:
            items.append(
                {
                    "item_name": item[0],
                    "item_code": item[1],
                    "item_conditions": " ",
                    "item_price": f"{item[2]}€",
                    "item_quantity": "1",
                    "item_subtotal": f"{item[2]}€",
                }
            )
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_userinfo = None
    try:
        # From gatchan.users get name, mobile_phone, email
        query = """
        SELECT name, mobile_phone, email, national_id, address, city, postal_code, province
        FROM gatchan.users
        WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s)
        """
        result_userinfo = db_manager.execute_query(query, (sale_id,))
        user_info = result_userinfo[0] if result_userinfo else None
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_shipping = None
    try:
        # from gatchan.shipping get address, city, postal_code, province. To get to shipping, we need to get uuid from sale_id and then get the sjipping_id from gatchan.users
        query = "SELECT address, city, postal_code, province FROM gatchan.shipping WHERE id = (SELECT shipping_id FROM gatchan.users WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s))"
        result_shipping = db_manager.execute_query(query, (sale_id,))
        shipping_info = result_shipping[0] if result_shipping else None

    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_bank = None
    try:
        # Get user iban from billing table
        query = "SELECT bank_account FROM gatchan.billing WHERE id = (SELECT billing_id FROM gatchan.users WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s))"
        result_bank = db_manager.execute_query(query, (sale_id,))
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_date = None
    try:
        query = "SELECT date FROM gatchan.sales WHERE id = %s"
        result_date = db_manager.execute_query(query, (sale_id,))
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    # Date format 2023-10-17 00:00:00
    sale_date = result_date[0][0] if result_date else None
    sale_date = sale_date.strftime("%d/%m/%Y") if sale_date else None

    html_content = """
    <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <div class="invoice">
                <div class="invoice-header">
                    <div class="logo">
                        <svg xmlns="http://www.w3.org/2000/svg"
                            class="logo-img" version="1.1"
                            viewBox="0.00 0.00 500.00 400.00">
                            <path fill="#02e2b6" d="
                            M 132.84 174.78
                            A 0.60 0.60 0.0 0 0 133.42 174.18
                            Q 133.36 167.38 133.17 159.56
                            C 133.01 153.01 139.25 153.12 143.89 153.63
                            Q 149.03 154.20 149.17 158.91
                            Q 149.39 166.22 149.22 173.65
                            A 1.27 1.26 88.4 0 0 150.58 174.94
                            C 152.79 174.77 159.45 173.95 160.43 176.56
                            Q 162.88 183.11 160.46 188.10
                            Q 159.45 190.20 157.13 190.11
                            Q 153.74 189.97 150.12 190.11
                            Q 149.30 190.14 149.29 190.96
                            Q 149.20 197.92 149.37 205.26
                            C 149.53 212.44 158.78 205.96 160.84 212.32
                            C 162.20 216.57 161.97 223.90 156.06 224.10
                            C 150.07 224.29 143.08 224.94 137.55 222.48
                            C 133.76 220.80 133.37 215.92 133.38 212.25
                            Q 133.39 201.55 133.41 191.04
                            A 0.87 0.87 0.0 0 0 132.52 190.16
                            C 129.66 190.19 126.08 190.92 125.11 187.23
                            Q 123.62 181.57 125.36 176.94
                            C 126.40 174.18 130.50 174.87 132.84 174.78
                            Z" />
                            <path fill="#02e2b6" d="
                            M 233.42 179.04
                            C 237.55 175.10 243.32 174.27 248.87 175.66
                            Q 259.39 178.32 264.06 189.41
                            Q 266.02 194.07 266.02 198.49
                            Q 266.01 207.83 265.99 217.99
                            Q 265.97 223.13 260.92 223.93
                            C 256.60 224.62 249.85 224.46 249.79 218.43
                            Q 249.68 209.09 249.69 199.50
                            Q 249.70 194.72 246.05 192.23
                            C 241.45 189.09 232.63 192.44 232.57 198.76
                            Q 232.49 209.23 232.59 217.43
                            C 232.65 221.87 230.22 224.15 225.81 224.17
                            C 221.67 224.19 216.59 224.21 216.58 218.56
                            Q 216.52 187.58 216.64 160.39
                            Q 216.65 158.74 216.80 157.18
                            C 217.23 152.86 225.16 153.25 227.94 153.72
                            Q 232.44 154.49 232.49 159.27
                            Q 232.58 168.56 232.57 178.68
                            A 0.50 0.50 0.0 0 0 233.42 179.04
                            Z" />
                            <path fill="#02e2b6" d="
                            M 45.51 179.31
                            A 0.55 0.55 0.0 0 0 46.42 179.06
                            C 47.15 176.51 48.97 175.04 51.59 174.91
                            C 57.66 174.62 62.05 174.14 62.10 181.85
                            Q 62.30 212.88 61.87 224.12
                            C 61.55 232.60 55.85 239.64 48.25 243.14
                            Q 37.31 248.19 24.83 242.56
                            C 21.73 241.16 16.69 238.61 17.63 234.57
                            Q 18.58 230.46 22.96 227.20
                            C 24.52 226.04 26.42 226.39 27.87 227.47
                            Q 31.44 230.11 36.30 230.71
                            C 40.93 231.28 45.16 228.72 45.88 224.01
                            Q 46.11 222.53 46.29 221.12
                            Q 46.62 218.43 44.52 220.14
                            C 41.45 222.65 36.28 222.91 32.41 222.44
                            Q 27.78 221.88 24.11 219.15
                            Q 17.80 214.47 15.50 209.14
                            Q 10.54 197.65 15.95 187.65
                            C 21.58 177.25 35.39 169.82 45.51 179.31
                            Z
                            M 45.8059 199.3087
                            A 8.49 8.28 3.3 0 0 37.8066 190.5537
                            A 8.49 8.28 3.3 0 0 28.8541 198.3313
                            A 8.49 8.28 3.3 0 0 36.8534 207.0863
                            A 8.49 8.28 3.3 0 0 45.8059 199.3087
                            Z" />
                            <path fill="#02e2b6" d="
                            M 188.73 207.39
                            C 192.43 208.92 196.24 207.41 199.46 205.34
                            Q 201.59 203.97 203.60 205.23
                            C 206.82 207.23 211.82 212.16 209.80 216.25
                            Q 208.80 218.28 206.20 219.99
                            C 195.94 226.72 183.84 225.04 174.73 217.23
                            Q 169.68 212.90 167.76 205.16
                            Q 165.77 197.14 169.32 188.91
                            C 172.55 181.42 180.94 176.01 188.67 174.99
                            Q 197.84 173.79 206.14 178.89
                            Q 208.24 180.19 209.55 181.89
                            C 212.07 185.20 208.18 190.37 205.74 192.77
                            C 202.46 195.99 199.29 193.66 196.23 191.84
                            C 193.26 190.08 189.38 190.78 186.73 192.80
                            C 181.43 196.83 182.44 204.78 188.73 207.39
                            Z" />
                            <path fill="#02e2b6" d="
                            M 102.52 179.43
                            L 103.97 177.12
                            Q 104.27 176.64 104.75 176.33
                            C 107.61 174.45 111.80 174.71 115.02 175.35
                            C 119.02 176.14 118.73 181.55 118.75 184.58
                            Q 118.87 200.60 118.64 218.38
                            C 118.55 224.89 111.61 224.51 107.07 223.62
                            Q 103.52 222.93 103.05 219.99
                            A 0.60 0.60 0.0 0 0 102.07 219.63
                            C 101.05 220.47 100.29 221.58 99.07 222.15
                            Q 91.18 225.82 83.87 222.42
                            C 64.43 213.39 63.73 187.12 82.97 177.07
                            Q 91.28 172.74 99.26 177.31
                            C 100.20 177.86 100.98 178.42 101.47 179.39
                            Q 101.95 180.34 102.52 179.43
                            Z
                            M 102.1957 199.1792
                            A 8.62 8.46 -1.8 0 0 93.3143 190.9942
                            A 8.62 8.46 -1.8 0 0 84.9643 199.7208
                            A 8.62 8.46 -1.8 0 0 93.8457 207.9058
                            A 8.62 8.46 -1.8 0 0 102.1957 199.1792
                            Z" />
                            <path fill="#02e2b6" d="
                            M 305.83 219.73
                            C 304.96 220.68 303.97 221.63 302.79 222.18
                            C 293.52 226.52 284.96 222.73 278.60 215.44
                            C 267.38 202.56 272.87 183.51 287.33 176.81
                            Q 297.61 172.05 305.65 179.38
                            Q 306.24 179.92 306.57 179.19
                            Q 307.00 178.25 307.61 177.42
                            C 310.16 173.90 317.14 174.65 320.47 175.95
                            Q 320.98 176.15 321.27 176.62
                            Q 322.99 179.43 322.98 182.65
                            Q 322.89 199.07 322.89 217.03
                            Q 322.89 219.61 321.60 221.69
                            C 319.45 225.15 312.36 224.18 309.32 223.03
                            C 307.90 222.49 307.26 221.09 306.61 219.83
                            A 0.48 0.48 0.0 0 0 305.83 219.73
                            Z
                            M 305.90 199.46
                            A 8.51 8.51 0.0 0 0 297.39 190.95
                            A 8.51 8.51 0.0 0 0 288.88 199.46
                            A 8.51 8.51 0.0 0 0 297.39 207.97
                            A 8.51 8.51 0.0 0 0 305.90 199.46
                            Z" />
                            <path fill="#02e2b6" d="
                            M 347.35 180.22
                            C 350.47 177.39 354.11 174.86 358.45 174.92
                            C 371.87 175.12 380.28 186.49 380.41 199.25
                            Q 380.49 206.72 380.44 214.24
                            Q 380.41 218.94 380.05 220.27
                            C 378.92 224.41 373.23 224.40 369.81 224.01
                            Q 364.16 223.37 364.11 217.68
                            Q 364.02 207.96 364.00 198.63
                            C 363.99 188.11 346.73 188.37 346.94 199.55
                            Q 347.13 209.96 346.91 218.23
                            Q 346.78 222.97 342.18 223.84
                            C 338.47 224.55 331.02 224.47 331.02 219.29
                            Q 330.99 201.46 330.97 180.46
                            Q 330.97 175.89 335.38 175.19
                            C 339.11 174.60 346.59 173.99 346.54 179.85
                            Q 346.53 180.97 347.35 180.22
                            Z" />
                            <path fill="#02e2b6" d="
                            M 400.41 188.78
                            C 404.98 175.09 420.15 171.10 431.55 179.06
                            Q 436.19 182.29 438.31 188.79
                            C 440.31 194.92 431.87 198.11 430.01 192.22
                            C 429.00 189.03 427.31 185.95 423.91 184.95
                            Q 412.22 181.50 408.96 191.81
                            Q 407.57 196.18 403.20 194.95
                            Q 403.05 194.91 402.92 194.84
                            Q 399.07 192.80 400.41 188.78
                            Z" />
                            <path fill="#02e2b6" d="
                            M 461.31 185.68
                            C 458.58 187.14 458.05 190.06 456.98 192.65
                            C 455.60 195.98 451.17 195.99 449.23 193.23
                            C 447.19 190.32 450.31 184.39 452.08 182.16
                            C 456.16 177.03 463.98 174.06 470.36 175.16
                            C 478.28 176.52 484.50 181.28 486.52 188.99
                            C 487.50 192.74 485.28 195.85 481.21 195.10
                            Q 479.47 194.78 478.75 193.23
                            C 477.04 189.50 476.23 186.31 471.87 184.81
                            Q 466.41 182.94 461.31 185.68
                            Z" />
                        </svg>
                    </div>
                    <br>
                    <h1 class="title">Resumen de compra</h1>
                    <table class="invoiceInfo">
                        <tr>
                            <td class="invoiceHeader"># de pedido: </td>
                            <td class="invoiceNumber">
                            {{ invoiceNumber }}
                            </td>
                            <td class="invoiceDate">
                            {{ invoiceDate }}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="invoice-body">
                    <div class="invoiceSummary">
                        <h2 class="invoiceSummary">Resumen de pedido</h2>
                        <br>
                        <table class="invoiceSummary-table">
                            <!-- Section 1: Datos del cliente -->
                            <tr><th colspan="4" class="table-title">Datos del
                                    cliente:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Nombre/Razón Social:</th>
                                <td class="start">{{ customerName }}
                                    </td>
                                <th class="end">Teléfono de contacto:</th>
                                <td class="end">{{ customerPhone }}</td>
                            </tr>
                            <tr>
                                <th class="start">DNI/NIF</th>
                                <td class="start">{{ customerDni }}</td>
                                <th class="end">Email:</th>
                                <td class="end">{{ customerEmail }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>

                            <!-- Section 2: Instalación -->
                            <tr><th colspan="4" class="table-title">Instalación:</th></tr>
                            <tr><th class="start">&nbsp;</th></tr>
                            <tr>
                                <th class="start">Dirección:</th>
                                <td class="start">{{ installationAddress }}</td>
                                <th class="end">Población:</th>
                                <td class="end">{{ installationCity }}</td>
                            </tr>
                            <tr>
                                <th class="start">Código Postal:</th>
                                <td class="start">{{ installationPostalCode }}</td>
                                <th class="end">Provincia:</th>
                                <td class="end">{{ installationProvince }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <!-- Section 3: Envío -->
                            <tr><th colspan="4" class="table-title">Envío:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Dirección:</th>
                                <td class="start">{{ shippingAddress }}</td>
                                <th class="end">Población:</th>
                                <td class="end">{{ shippingCity }}</td>
                            </tr>
                            <tr>
                                <th class="start">Código Postal:</th>
                                <td class="start">{{ shippingPostalCode }}</td>
                                <th class="end">Provincia:</th>
                                <td class="end">{{ shippingProvince }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <!-- Section 4: Pago -->
                            <tr><th colspan="4" class="table-title">Pago:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Cuenta bancaria:</th>
                                <td class="start">{{ bankAccount }}</td>
                                <th class="start">&nbsp;</th>
                                <td class="start">&nbsp;</td>
                            </tr>
                        </table>

                        <br>

                        <table class="pricing-table">
                            <tr class="pricing-title">
                                <th>
                                    Primera Factura:
                                </th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr class="pricing-title">
                                <td>Artículo</td>
                                <td>Código:</td>
                                <td>Condiciones Generales de contratación:</td>
                                <td>Precio:</td>
                                <td>Cantidad:</td>
                                <td>Subtotal:</td>
                            </tr>

                            {{ items }}

                            <tr class="pricing-footer">
                                <td colspan="4"></td>
                                <td class="pricing-footer-title">Total (Inc iva):</td>
                                <td class="pricing-footer-value">{{ invoiceTotal }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="extras">
                        <div class="observaciones minh50">
                            <div class="left">
                                Observaciones
                            </div>
                            <div class="right">
                            {{ observations }}
                            </div>
                        </div>

                        <div class="promos">
                            <div class="observaciones minh20">
                                <div class="left">
                                    Regalo promo trae amigo del cliente anfitrión:
                                </div>
                                <div class="right">{{ giftHost }}
                                </div>
                            </div>

                            <div class="observaciones minh20">
                                <div class="left">
                                    Regalo promo trae amigo del invitado:
                                </div>
                                <div class="right"> {{ giftGuest }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="invoice-footer">
                    <span>Firma del cliente.</span>
                </div>
            </div>
        </body>

        <style>
            @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

    /* GENERAL STUFF */
    body {
        font-family: Roboto, Arial, sans-serif;
        margin: 0;
        padding: 0;
        /* background-color: #1b1b1b; */

        font-size: 10px;
    }

    .invoice {
        width: 80%;
        max-width: 800px;
        margin: 30px auto;
        padding: 50px;
        background-color: #fff;
        
        /* border: 1px solid #ddd; */
        /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); */
    }

    .invoice-body,
    .invoice-footer {
        margin-bottom: 20px;
    }

    table {
        width: 100%;
    }

    th {
        text-align: left;
    }

    td {
        text-align: center;
    }


    /* INVOICE HEADER */

    .invoice-header {
        margin-bottom: 10px;
    }

    .logo {
        /* Be on the right */
        float: right;
    }

    .logo-img {
        width: 100px;
    }

    h1.title {
        margin: 0;
        margin-bottom: 3px;
        font-size: 1.5em;
        line-height: 1.5em;

        color: #6673c4;
    }

    td.invoiceHeader {
        font-weight: bold;
        text-align: left;
    }

    td.invoiceDate,
    td.invoiceNumber {
        text-align: right;
    }

    /* INVOICE BODY */
    .invoice-body {
        /* border-top: 1px solid #ddd; */
        /* border-bottom: 1px solid #ddd; */
        padding: 0px 0;
    }

    h2.invoiceSummary {
        margin: 0;
        font-size: 1em;
        line-height: 1.5em;

        color: #467ece;
    }

    h3.table-title,
    th.table-title {
        width: 100%;
        padding: 0 0;
        font-size: 1em;
        background-color: #00e3b9;
        text-align: center;
        border: none;
    }

    tr.spacer td {
        padding: 3px 0;
        line-height: 0;
    }

    td.start {
        text-align: left;
    }

    th.end {
        text-align: left;
    }

    tr {
        padding-bottom: 20px;
    }

    .pricing-table {
        width: 100%;
        border-collapse: collapse;
    }

    .pricing-title {
        font-weight: bold;
        /* Draw a line under */
        border-bottom: 1px solid #000;
    }

    .pricing-table td:nth-child(1),
    .pricing-table td:nth-child(2) {
        width: 25%;
    }

    .pricing-table td:nth-child(3),
    .pricing-table td:nth-child(4),
    .pricing-table td:nth-child(5),
    .pricing-table td:nth-child(6) {
        width: 12.5%;
    }

    .pricing-table td {
        vertical-align: bottom;
        text-align: left;
    }

    .pricing-item td {
        vertical-align: top;
        text-align: left;
        padding-bottom: 5px;
    }

    .pricing-footer {
        font-weight: bold;
        border-top: 1px solid #000;
    }

    .extras {
        margin-top: 30px;
    }

    .observaciones {
        display: flex;
        font-weight: bold;

    }

    .minh50 {
        min-height: 50px;
    }

    .minh20 {
        min-height: 20px;
    }

    .observaciones .left {
        flex: 30%;
    }

    .observaciones .right {
        flex: 50%;
        font-weight: normal;
    }

    /* INVOICE FOOTER */
    .invoice-footer {
        margin-top: 100px;

        position: relative;
        text-align: right;
    }

    .invoice-footer span {
        border-top: 1px solid #000;
        display: inline-block;
        font-weight: bold;
        padding: 0 3px;
    }
        </style>
    </html>
    """

    # I want to access the var "name" here
    name = user_info[0] if user_info else None
    mobile_phone = user_info[1] if user_info else None
    email = user_info[2] if user_info else None
    national_id = user_info[3] if user_info else None
    address = user_info[4] if user_info else None
    city = user_info[5] if user_info else None
    postal_code = user_info[6] if user_info else None
    province = user_info[7] if user_info else None

    shipping_address = shipping_info[0] if shipping_info else None
    shipping_city = shipping_info[1] if shipping_info else None
    shipping_postal_code = shipping_info[2] if shipping_info else None
    shipping_province = shipping_info[3] if shipping_info else None
    bank_account = result_bank[0][0] if result else None

    if bank_account:
        bank_account = '*' * (len(bank_account) - 4) + bank_account[-4:]

    # get invoice total
    invoice_total = 0
    for item in items:
        invoice_total += float(item["item_price"].replace("€", "").replace(",", "."))

    # Clean decimal places
    invoice_total = round(invoice_total, 2)
    # Add 21% iva
    invoice_total = round(invoice_total * 1.21, 2)

    variables = {
        "invoiceNumber": sale_id,
        "invoiceDate": sale_date,
        "customerName": name,
        "customerDni": national_id,
        "customerPhone": mobile_phone,
        "customerEmail": email,
        "installationAddress": address,
        "installationCity": city,
        "installationPostalCode": postal_code,
        "installationProvince": province,
        "shippingAddress": shipping_address,
        "shippingCity": shipping_city,
        "shippingPostalCode": shipping_postal_code,
        "shippingProvince": shipping_province,
        "bankAccount": bank_account,
        "items": generate_items(items),
        "invoiceTotal": f"{invoice_total}€",
        "observations": "",
        "giftHost": "",
        "giftGuest": "",
    }

    rendered_html = Template(html_content).render(variables)
    return rendered_html


def portabilidadHtml(sale_id: str, phoneNum: str):
    def generate_items(items):
        html_item = """
        <tr>
            <td>{{ item_name }}</td>
            <td>{{ item_code }}</td>
            <td>{{ item_conditions }}</td>
            <td>{{ item_price }}</td>
            <td>{{ item_quantity }}</td>
            <td>{{ item_subtotal }}</td>
        </tr>
        """
        html_content = ""
        for item in items:
            html_content += Template(html_item).render(item)
        return html_content


    try:
        # Get all sale details from the database.
        query = """
        SELECT items.name AS item_name, items.product_id AS item_code, items.price FROM gatchan.sale_details JOIN gatchan.items ON sale_details.item_id = items.id WHERE sale_details.sale_id = %s;
        """
        print(f"Executing query with sale_id: {sale_id}")
        result = db_manager.execute_query(query, (sale_id,))

        # Tuple of tuples, (name, code, Decimal('price'))
        items = []
        for item in result:
            items.append(
                {
                    "item_name": item[0],
                    "item_code": item[1],
                    "item_conditions": " ",
                    "item_price": f"{item[2]}€",
                    "item_quantity": "1",
                    "item_subtotal": f"{item[2]}€",
                }
            )
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_userinfo = None
    try:
        # From gatchan.users get name, mobile_phone, email
        query = """
        SELECT name, mobile_phone, email, national_id, address, city, postal_code, province
        FROM gatchan.users
        WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s)
        """
        result_userinfo = db_manager.execute_query(query, (sale_id,))
        user_info = result_userinfo[0] if result_userinfo else None
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_shipping = None
    try:
        # from gatchan.shipping get address, city, postal_code, province. To get to shipping, we need to get uuid from sale_id and then get the sjipping_id from gatchan.users
        query = "SELECT address, city, postal_code, province FROM gatchan.shipping WHERE id = (SELECT shipping_id FROM gatchan.users WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s))"
        result_shipping = db_manager.execute_query(query, (sale_id,))
        shipping_info = result_shipping[0] if result_shipping else None

    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_bank = None
    try:
        # Get user iban from billing table
        query = "SELECT bank_account FROM gatchan.billing WHERE id = (SELECT billing_id FROM gatchan.users WHERE uuid = (SELECT user_id FROM gatchan.sales WHERE id = %s))"
        result_bank = db_manager.execute_query(query, (sale_id,))
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    result_date = None
    try:
        query = "SELECT date FROM gatchan.sales WHERE id = %s"
        result_date = db_manager.execute_query(query, (sale_id,))
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    # Date format 2023-10-17 00:00:00
    sale_date = result_date[0][0] if result_date else None
    sale_date = sale_date.strftime("%d/%m/%Y") if sale_date else None

    html_content = """
    <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <div class="invoice">
                <div class="invoice-header">
                    <div class="logo">
                        <svg xmlns="http://www.w3.org/2000/svg"
                            class="logo-img" version="1.1"
                            viewBox="0.00 0.00 500.00 400.00">
                            <path fill="#02e2b6" d="
                            M 132.84 174.78
                            A 0.60 0.60 0.0 0 0 133.42 174.18
                            Q 133.36 167.38 133.17 159.56
                            C 133.01 153.01 139.25 153.12 143.89 153.63
                            Q 149.03 154.20 149.17 158.91
                            Q 149.39 166.22 149.22 173.65
                            A 1.27 1.26 88.4 0 0 150.58 174.94
                            C 152.79 174.77 159.45 173.95 160.43 176.56
                            Q 162.88 183.11 160.46 188.10
                            Q 159.45 190.20 157.13 190.11
                            Q 153.74 189.97 150.12 190.11
                            Q 149.30 190.14 149.29 190.96
                            Q 149.20 197.92 149.37 205.26
                            C 149.53 212.44 158.78 205.96 160.84 212.32
                            C 162.20 216.57 161.97 223.90 156.06 224.10
                            C 150.07 224.29 143.08 224.94 137.55 222.48
                            C 133.76 220.80 133.37 215.92 133.38 212.25
                            Q 133.39 201.55 133.41 191.04
                            A 0.87 0.87 0.0 0 0 132.52 190.16
                            C 129.66 190.19 126.08 190.92 125.11 187.23
                            Q 123.62 181.57 125.36 176.94
                            C 126.40 174.18 130.50 174.87 132.84 174.78
                            Z" />
                            <path fill="#02e2b6" d="
                            M 233.42 179.04
                            C 237.55 175.10 243.32 174.27 248.87 175.66
                            Q 259.39 178.32 264.06 189.41
                            Q 266.02 194.07 266.02 198.49
                            Q 266.01 207.83 265.99 217.99
                            Q 265.97 223.13 260.92 223.93
                            C 256.60 224.62 249.85 224.46 249.79 218.43
                            Q 249.68 209.09 249.69 199.50
                            Q 249.70 194.72 246.05 192.23
                            C 241.45 189.09 232.63 192.44 232.57 198.76
                            Q 232.49 209.23 232.59 217.43
                            C 232.65 221.87 230.22 224.15 225.81 224.17
                            C 221.67 224.19 216.59 224.21 216.58 218.56
                            Q 216.52 187.58 216.64 160.39
                            Q 216.65 158.74 216.80 157.18
                            C 217.23 152.86 225.16 153.25 227.94 153.72
                            Q 232.44 154.49 232.49 159.27
                            Q 232.58 168.56 232.57 178.68
                            A 0.50 0.50 0.0 0 0 233.42 179.04
                            Z" />
                            <path fill="#02e2b6" d="
                            M 45.51 179.31
                            A 0.55 0.55 0.0 0 0 46.42 179.06
                            C 47.15 176.51 48.97 175.04 51.59 174.91
                            C 57.66 174.62 62.05 174.14 62.10 181.85
                            Q 62.30 212.88 61.87 224.12
                            C 61.55 232.60 55.85 239.64 48.25 243.14
                            Q 37.31 248.19 24.83 242.56
                            C 21.73 241.16 16.69 238.61 17.63 234.57
                            Q 18.58 230.46 22.96 227.20
                            C 24.52 226.04 26.42 226.39 27.87 227.47
                            Q 31.44 230.11 36.30 230.71
                            C 40.93 231.28 45.16 228.72 45.88 224.01
                            Q 46.11 222.53 46.29 221.12
                            Q 46.62 218.43 44.52 220.14
                            C 41.45 222.65 36.28 222.91 32.41 222.44
                            Q 27.78 221.88 24.11 219.15
                            Q 17.80 214.47 15.50 209.14
                            Q 10.54 197.65 15.95 187.65
                            C 21.58 177.25 35.39 169.82 45.51 179.31
                            Z
                            M 45.8059 199.3087
                            A 8.49 8.28 3.3 0 0 37.8066 190.5537
                            A 8.49 8.28 3.3 0 0 28.8541 198.3313
                            A 8.49 8.28 3.3 0 0 36.8534 207.0863
                            A 8.49 8.28 3.3 0 0 45.8059 199.3087
                            Z" />
                            <path fill="#02e2b6" d="
                            M 188.73 207.39
                            C 192.43 208.92 196.24 207.41 199.46 205.34
                            Q 201.59 203.97 203.60 205.23
                            C 206.82 207.23 211.82 212.16 209.80 216.25
                            Q 208.80 218.28 206.20 219.99
                            C 195.94 226.72 183.84 225.04 174.73 217.23
                            Q 169.68 212.90 167.76 205.16
                            Q 165.77 197.14 169.32 188.91
                            C 172.55 181.42 180.94 176.01 188.67 174.99
                            Q 197.84 173.79 206.14 178.89
                            Q 208.24 180.19 209.55 181.89
                            C 212.07 185.20 208.18 190.37 205.74 192.77
                            C 202.46 195.99 199.29 193.66 196.23 191.84
                            C 193.26 190.08 189.38 190.78 186.73 192.80
                            C 181.43 196.83 182.44 204.78 188.73 207.39
                            Z" />
                            <path fill="#02e2b6" d="
                            M 102.52 179.43
                            L 103.97 177.12
                            Q 104.27 176.64 104.75 176.33
                            C 107.61 174.45 111.80 174.71 115.02 175.35
                            C 119.02 176.14 118.73 181.55 118.75 184.58
                            Q 118.87 200.60 118.64 218.38
                            C 118.55 224.89 111.61 224.51 107.07 223.62
                            Q 103.52 222.93 103.05 219.99
                            A 0.60 0.60 0.0 0 0 102.07 219.63
                            C 101.05 220.47 100.29 221.58 99.07 222.15
                            Q 91.18 225.82 83.87 222.42
                            C 64.43 213.39 63.73 187.12 82.97 177.07
                            Q 91.28 172.74 99.26 177.31
                            C 100.20 177.86 100.98 178.42 101.47 179.39
                            Q 101.95 180.34 102.52 179.43
                            Z
                            M 102.1957 199.1792
                            A 8.62 8.46 -1.8 0 0 93.3143 190.9942
                            A 8.62 8.46 -1.8 0 0 84.9643 199.7208
                            A 8.62 8.46 -1.8 0 0 93.8457 207.9058
                            A 8.62 8.46 -1.8 0 0 102.1957 199.1792
                            Z" />
                            <path fill="#02e2b6" d="
                            M 305.83 219.73
                            C 304.96 220.68 303.97 221.63 302.79 222.18
                            C 293.52 226.52 284.96 222.73 278.60 215.44
                            C 267.38 202.56 272.87 183.51 287.33 176.81
                            Q 297.61 172.05 305.65 179.38
                            Q 306.24 179.92 306.57 179.19
                            Q 307.00 178.25 307.61 177.42
                            C 310.16 173.90 317.14 174.65 320.47 175.95
                            Q 320.98 176.15 321.27 176.62
                            Q 322.99 179.43 322.98 182.65
                            Q 322.89 199.07 322.89 217.03
                            Q 322.89 219.61 321.60 221.69
                            C 319.45 225.15 312.36 224.18 309.32 223.03
                            C 307.90 222.49 307.26 221.09 306.61 219.83
                            A 0.48 0.48 0.0 0 0 305.83 219.73
                            Z
                            M 305.90 199.46
                            A 8.51 8.51 0.0 0 0 297.39 190.95
                            A 8.51 8.51 0.0 0 0 288.88 199.46
                            A 8.51 8.51 0.0 0 0 297.39 207.97
                            A 8.51 8.51 0.0 0 0 305.90 199.46
                            Z" />
                            <path fill="#02e2b6" d="
                            M 347.35 180.22
                            C 350.47 177.39 354.11 174.86 358.45 174.92
                            C 371.87 175.12 380.28 186.49 380.41 199.25
                            Q 380.49 206.72 380.44 214.24
                            Q 380.41 218.94 380.05 220.27
                            C 378.92 224.41 373.23 224.40 369.81 224.01
                            Q 364.16 223.37 364.11 217.68
                            Q 364.02 207.96 364.00 198.63
                            C 363.99 188.11 346.73 188.37 346.94 199.55
                            Q 347.13 209.96 346.91 218.23
                            Q 346.78 222.97 342.18 223.84
                            C 338.47 224.55 331.02 224.47 331.02 219.29
                            Q 330.99 201.46 330.97 180.46
                            Q 330.97 175.89 335.38 175.19
                            C 339.11 174.60 346.59 173.99 346.54 179.85
                            Q 346.53 180.97 347.35 180.22
                            Z" />
                            <path fill="#02e2b6" d="
                            M 400.41 188.78
                            C 404.98 175.09 420.15 171.10 431.55 179.06
                            Q 436.19 182.29 438.31 188.79
                            C 440.31 194.92 431.87 198.11 430.01 192.22
                            C 429.00 189.03 427.31 185.95 423.91 184.95
                            Q 412.22 181.50 408.96 191.81
                            Q 407.57 196.18 403.20 194.95
                            Q 403.05 194.91 402.92 194.84
                            Q 399.07 192.80 400.41 188.78
                            Z" />
                            <path fill="#02e2b6" d="
                            M 461.31 185.68
                            C 458.58 187.14 458.05 190.06 456.98 192.65
                            C 455.60 195.98 451.17 195.99 449.23 193.23
                            C 447.19 190.32 450.31 184.39 452.08 182.16
                            C 456.16 177.03 463.98 174.06 470.36 175.16
                            C 478.28 176.52 484.50 181.28 486.52 188.99
                            C 487.50 192.74 485.28 195.85 481.21 195.10
                            Q 479.47 194.78 478.75 193.23
                            C 477.04 189.50 476.23 186.31 471.87 184.81
                            Q 466.41 182.94 461.31 185.68
                            Z" />
                        </svg>
                    </div>
                    <br>
                    <h1 class="title">Resumen de compra</h1>
                    <table class="invoiceInfo">
                        <tr>
                            <td class="invoiceHeader"># de pedido: </td>
                            <td class="invoiceNumber">
                            {{ invoiceNumber }}
                            </td>
                            <td class="invoiceDate">
                            {{ invoiceDate }}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="invoice-body">
                    <div class="invoiceSummary">
                        <h2 class="invoiceSummary">Resumen de pedido</h2>
                        <br>
                        <table class="invoiceSummary-table">
                            <!-- Section 1: Datos del cliente -->
                            <tr><th colspan="4" class="table-title">Datos del
                                    cliente:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Nombre/Razón Social:</th>
                                <td class="start">{{ customerName }}
                                    </td>
                                <th class="end">Teléfono de contacto:</th>
                                <td class="end">{{ customerPhone }}</td>
                            </tr>
                            <tr>
                                <th class="start">DNI/NIF</th>
                                <td class="start">{{ customerDni }}</td>
                                <th class="end">Email:</th>
                                <td class="end">{{ customerEmail }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>

                            <!-- Section 2: Instalación -->
                            <tr><th colspan="4" class="table-title">Instalación:</th></tr>
                            <tr><th class="start">&nbsp;</th></tr>
                            <tr>
                                <th class="start">Dirección:</th>
                                <td class="start">{{ installationAddress }}</td>
                                <th class="end">Población:</th>
                                <td class="end">{{ installationCity }}</td>
                            </tr>
                            <tr>
                                <th class="start">Código Postal:</th>
                                <td class="start">{{ installationPostalCode }}</td>
                                <th class="end">Provincia:</th>
                                <td class="end">{{ installationProvince }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <!-- Section 3: Envío -->
                            <tr><th colspan="4" class="table-title">Envío:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Dirección:</th>
                                <td class="start">{{ shippingAddress }}</td>
                                <th class="end">Población:</th>
                                <td class="end">{{ shippingCity }}</td>
                            </tr>
                            <tr>
                                <th class="start">Código Postal:</th>
                                <td class="start">{{ shippingPostalCode }}</td>
                                <th class="end">Provincia:</th>
                                <td class="end">{{ shippingProvince }}</td>
                            </tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <!-- Section 4: Pago -->
                            <tr><th colspan="4" class="table-title">Pago:</th></tr>
                            <tr class="spacer"><td colspan="100%"></td></tr>
                            <tr>
                                <th class="start">Cuenta bancaria:</th>
                                <td class="start">{{ bankAccount }}</td>
                                <th class="start">&nbsp;</th>
                                <td class="start">&nbsp;</td>
                            </tr>
                        </table>

                        <br>

                        <table class="pricing-table">
                            <tr class="pricing-title">
                                <th>
                                    Primera Factura:
                                </th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr class="pricing-title">
                                <td>Artículo</td>
                                <td>Código:</td>
                                <td>Condiciones Generales de contratación:</td>
                                <td>Precio:</td>
                                <td>Cantidad:</td>
                                <td>Subtotal:</td>
                            </tr>

                            {{ items }}

                            <tr class="pricing-footer">
                                <td colspan="4">{{ phoneNumber }}</td>
                                <td class="pricing-footer-title">Total (Inc iva):</td>
                                <td class="pricing-footer-value">{{ invoiceTotal }}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="extras">
                        <div class="observaciones minh50">
                            <div class="left">
                                Observaciones
                            </div>
                            <div class="right">
                            {{ observations }}
                            </div>
                        </div>

                        <div class="promos">
                            <div class="observaciones minh20">
                                <div class="left">
                                    Regalo promo trae amigo del cliente anfitrión:
                                </div>
                                <div class="right">{{ giftHost }}
                                </div>
                            </div>

                            <div class="observaciones minh20">
                                <div class="left">
                                    Regalo promo trae amigo del invitado:
                                </div>
                                <div class="right"> {{ giftGuest }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="invoice-footer">
                    <span>Firma del cliente.</span>
                </div>
            </div>
        </body>

        <style>
            @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

    /* GENERAL STUFF */
    body {
        font-family: Roboto, Arial, sans-serif;
        margin: 0;
        padding: 0;
        /* background-color: #1b1b1b; */

        font-size: 10px;
    }

    .invoice {
        width: 80%;
        max-width: 800px;
        margin: 30px auto;
        padding: 50px;
        background-color: #fff;
        
        /* border: 1px solid #ddd; */
        /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); */
    }

    .invoice-body,
    .invoice-footer {
        margin-bottom: 20px;
    }

    table {
        width: 100%;
    }

    th {
        text-align: left;
    }

    td {
        text-align: center;
    }


    /* INVOICE HEADER */

    .invoice-header {
        margin-bottom: 10px;
    }

    .logo {
        /* Be on the right */
        float: right;
    }

    .logo-img {
        width: 100px;
    }

    h1.title {
        margin: 0;
        margin-bottom: 3px;
        font-size: 1.5em;
        line-height: 1.5em;

        color: #6673c4;
    }

    td.invoiceHeader {
        font-weight: bold;
        text-align: left;
    }

    td.invoiceDate,
    td.invoiceNumber {
        text-align: right;
    }

    /* INVOICE BODY */
    .invoice-body {
        /* border-top: 1px solid #ddd; */
        /* border-bottom: 1px solid #ddd; */
        padding: 0px 0;
    }

    h2.invoiceSummary {
        margin: 0;
        font-size: 1em;
        line-height: 1.5em;

        color: #467ece;
    }

    h3.table-title,
    th.table-title {
        width: 100%;
        padding: 0 0;
        font-size: 1em;
        background-color: #00e3b9;
        text-align: center;
        border: none;
    }

    tr.spacer td {
        padding: 3px 0;
        line-height: 0;
    }

    td.start {
        text-align: left;
    }

    th.end {
        text-align: left;
    }

    tr {
        padding-bottom: 20px;
    }

    .pricing-table {
        width: 100%;
        border-collapse: collapse;
    }

    .pricing-title {
        font-weight: bold;
        /* Draw a line under */
        border-bottom: 1px solid #000;
    }

    .pricing-table td:nth-child(1),
    .pricing-table td:nth-child(2) {
        width: 25%;
    }

    .pricing-table td:nth-child(3),
    .pricing-table td:nth-child(4),
    .pricing-table td:nth-child(5),
    .pricing-table td:nth-child(6) {
        width: 12.5%;
    }

    .pricing-table td {
        vertical-align: bottom;
        text-align: left;
    }

    .pricing-item td {
        vertical-align: top;
        text-align: left;
        padding-bottom: 5px;
    }

    .pricing-footer {
        font-weight: bold;
        border-top: 1px solid #000;
    }

    .extras {
        margin-top: 30px;
    }

    .observaciones {
        display: flex;
        font-weight: bold;

    }

    .minh50 {
        min-height: 50px;
    }

    .minh20 {
        min-height: 20px;
    }

    .observaciones .left {
        flex: 30%;
    }

    .observaciones .right {
        flex: 50%;
        font-weight: normal;
    }

    /* INVOICE FOOTER */
    .invoice-footer {
        margin-top: 100px;

        position: relative;
        text-align: right;
    }

    .invoice-footer span {
        border-top: 1px solid #000;
        display: inline-block;
        font-weight: bold;
        padding: 0 3px;
    }
        </style>
    </html>
    """

    # I want to access the var "name" here
    name = user_info[0] if user_info else None
    mobile_phone = user_info[1] if user_info else None
    email = user_info[2] if user_info else None
    national_id = user_info[3] if user_info else None
    address = user_info[4] if user_info else None
    city = user_info[5] if user_info else None
    postal_code = user_info[6] if user_info else None
    province = user_info[7] if user_info else None

    shipping_address = shipping_info[0] if shipping_info else None
    shipping_city = shipping_info[1] if shipping_info else None
    shipping_postal_code = shipping_info[2] if shipping_info else None
    shipping_province = shipping_info[3] if shipping_info else None
    bank_account = result_bank[0][0] if result else None

    if bank_account:
        bank_account = '*' * (len(bank_account) - 4) + bank_account[-4:]

    # get invoice total
    invoice_total = 0
    for item in items:
        invoice_total += float(item["item_price"].replace("€", "").replace(",", "."))

    # Clean decimal places
    invoice_total = round(invoice_total, 2)
    # Add 21% iva
    invoice_total = round(invoice_total * 1.21, 2)

    variables = {
        "invoiceNumber": sale_id,
        "invoiceDate": sale_date,
        "customerName": name,
        "customerDni": national_id,
        "customerPhone": mobile_phone,
        "customerEmail": email,
        "installationAddress": address,
        "installationCity": city,
        "installationPostalCode": postal_code,
        "installationProvince": province,
        "shippingAddress": shipping_address,
        "shippingCity": shipping_city,
        "shippingPostalCode": shipping_postal_code,
        "shippingProvince": shipping_province,
        "bankAccount": bank_account,
        "items": generate_items(items),
        "invoiceTotal": f"{invoice_total}€",
        "observations": "",
        "giftHost": "",
        "giftGuest": "",
        "phoneNumber": phoneNum,
    }

    rendered_html = Template(html_content).render(variables)
    return rendered_html
